"""Main FastAPI application factory."""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .config import get_settings
from .events import lifespan

settings = get_settings()


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: The configured FastAPI application instance.
    """
    # Initialize FastAPI application with lifespan events
    app = FastAPI(
        title=settings.APP_NAME,
        description="API for downloading YouTube subtitles and summarizing text using AI",
        version=settings.APP_VERSION,
        docs_url=settings.DOCS_URL,
        redoc_url=settings.REDOC_URL,
        openapi_url=settings.OPENAPI_URL,
        lifespan=lifespan,
    )

    # Configure CORS
    configure_cors(app)

    # Add middleware
    configure_middleware(app)

    # Configure error handlers
    configure_error_handlers(app)

    # Include API routers
    configure_routers(app)

    # Add WebSocket routes
    configure_websocket_routes(app)

    # Health check is handled by the health router
    return app


def configure_cors(app: FastAPI) -> None:
    """Configure CORS middleware with security-conscious defaults."""
    from loguru import logger

    # Parse origins from comma-separated string
    origins = []
    if settings.CORS_ORIGINS == "*":
        origins = ["*"]
        # Log security warning with proper formatting
        logger.warning(
            "🚨 CORS configured to allow ALL origins (*) - CRITICAL SECURITY RISK in production! "
            "Set CORS_ORIGINS environment variable to specific domains like: "
            "'https://yourdomain.com,https://app.yourdomain.com'"
        )
    else:
        origins = [
            origin.strip()
            for origin in settings.CORS_ORIGINS.split(",")
            if origin.strip()
        ]
        logger.info(
            f"✅ CORS configured for {len(origins)} specific origins: {', '.join(origins)}"
        )

    # Parse methods from comma-separated string
    methods = [
        method.strip()
        for method in settings.CORS_ALLOW_METHODS.split(",")
        if method.strip()
    ]

    # Parse headers from comma-separated string
    headers = [
        header.strip()
        for header in settings.CORS_ALLOW_HEADERS.split(",")
        if header.strip()
    ]

    # Log CORS configuration details
    logger.debug(f"🔧 CORS methods: {', '.join(methods)}")
    logger.debug(f"🔧 CORS headers: {', '.join(headers)}")
    logger.debug(f"🔧 CORS credentials: {settings.CORS_ALLOW_CREDENTIALS}")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=methods,
        allow_headers=headers,
    )

    logger.info("✅ CORS middleware configured successfully")


def configure_middleware(app: FastAPI) -> None:
    """Configure application middleware."""
    from loguru import logger

    from api.middleware.auth import auth_middleware
    from api.middleware.metrics import metrics_middleware

    # Add Prometheus metrics middleware (first for accurate timing)
    prometheus_enabled = getattr(settings, "PROMETHEUS_ENABLED", True)
    if prometheus_enabled:
        from api.middleware.prometheus import PrometheusMiddleware

        app.add_middleware(PrometheusMiddleware, enabled=True)
        logger.info("✅ Prometheus metrics middleware enabled")

    # Add security headers middleware (after metrics to ensure all responses have security headers)
    if settings.SECURITY_HEADERS_ENABLED:
        from api.middleware.security import SecurityHeadersMiddleware

        app.add_middleware(SecurityHeadersMiddleware, enabled=True)

    # Add structured logging middleware (second to capture all requests)
    if settings.STRUCTURED_LOGGING:
        from api.middleware.logging import StructuredLoggingMiddleware

        app.add_middleware(
            StructuredLoggingMiddleware,
            exclude_paths=settings.LOG_EXCLUDE_PATHS,
            log_request_body=settings.LOG_REQUEST_BODY,
            log_response_body=settings.LOG_RESPONSE_BODY,
        )

    # Add rate limiting middleware (before auth to limit unauthenticated requests)
    if settings.RATE_LIMITING_ENABLED:
        from api.middleware.rate_limiting import (
            AdvancedRateLimitingMiddleware,
            EndpointLimits,
            RateLimitConfig,
            UserLimits,
        )

        # Create custom user limits based on configuration
        user_limits = UserLimits(
            anonymous=RateLimitConfig(
                requests_per_minute=settings.ANONYMOUS_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.ANONYMOUS_RATE_LIMIT_HOUR,
                requests_per_day=settings.ANONYMOUS_RATE_LIMIT_DAY,
                burst_limit=5,
            ),
            read_only=RateLimitConfig(
                requests_per_minute=settings.READ_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.READ_RATE_LIMIT_HOUR,
                requests_per_day=settings.READ_RATE_LIMIT_DAY,
                burst_limit=10,
            ),
            read_write=RateLimitConfig(
                requests_per_minute=settings.WRITE_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.WRITE_RATE_LIMIT_HOUR,
                requests_per_day=settings.WRITE_RATE_LIMIT_DAY,
                burst_limit=15,
            ),
            admin=RateLimitConfig(
                requests_per_minute=settings.ADMIN_RATE_LIMIT_MINUTE,
                requests_per_hour=settings.ADMIN_RATE_LIMIT_HOUR,
                requests_per_day=settings.ADMIN_RATE_LIMIT_DAY,
                burst_limit=25,
            ),
        )

        # Create custom endpoint limits
        endpoint_limits = EndpointLimits()
        endpoint_limits.subtitles = RateLimitConfig(
            requests_per_minute=settings.SUBTITLES_RATE_LIMIT_MINUTE,
            requests_per_hour=settings.SUBTITLES_RATE_LIMIT_HOUR,
            requests_per_day=settings.SUBTITLES_RATE_LIMIT_DAY,
            burst_limit=3,
        )
        endpoint_limits.summarize = RateLimitConfig(
            requests_per_minute=settings.SUMMARIZE_RATE_LIMIT_MINUTE,
            requests_per_hour=settings.SUMMARIZE_RATE_LIMIT_HOUR,
            requests_per_day=settings.SUMMARIZE_RATE_LIMIT_DAY,
            burst_limit=2,
        )

        app.add_middleware(
            AdvancedRateLimitingMiddleware,
            user_limits=user_limits,
            endpoint_limits=endpoint_limits,
            enabled=True,
        )

    # Add metrics collection middleware
    app.middleware("http")(metrics_middleware)

    # Add authentication middleware
    app.middleware("http")(auth_middleware)


def configure_routers(app: FastAPI) -> None:
    """Configure API routers."""
    # Import routers here to avoid circular imports
    from api.routers import auth, converter, health, subtitles, summarize, tasks

    # Include API routers
    app.include_router(health.router, tags=["health"])
    app.include_router(auth.router, prefix="/api", tags=["authentication"])
    app.include_router(subtitles.router, prefix="/api", tags=["subtitles"])
    app.include_router(summarize.router, prefix="/api", tags=["summarize"])
    app.include_router(tasks.router, prefix="/api", tags=["tasks"])
    app.include_router(converter.router, prefix="/api", tags=["converter"])

    # Add Prometheus metrics endpoint if enabled
    prometheus_enabled = getattr(settings, "PROMETHEUS_ENABLED", True)
    if prometheus_enabled:
        from api.middleware.prometheus import metrics_endpoint

        app.get("/metrics", response_class=None)(metrics_endpoint)


def configure_websocket_routes(app: FastAPI) -> None:
    """Configure WebSocket routes."""
    # Import WebSocket routers here to avoid circular imports
    from api.websockets import subtitles_ws, summarize_ws

    # Include WebSocket routers
    app.include_router(subtitles_ws.router)
    app.include_router(summarize_ws.router)


def configure_error_handlers(app: FastAPI) -> None:
    """Configure error handlers."""
    from fastapi import HTTPException
    from fastapi.exceptions import RequestValidationError

    from api.middleware.error_handler import (
        APIError,
        api_error_handler,
        general_exception_handler,
        http_exception_handler,
        validation_exception_handler,
    )

    # Add custom error handlers
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
