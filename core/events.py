"""
Application lifecycle events.
"""

from contextlib import asynccontextmanager

from fastapi import FastAPI
from loguru import logger

from worker.queue import TaskQueue


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Управление жизненным циклом приложения.

    Args:
        app: FastAPI application instance
    """
    # Startup
    logger.info("Application startup: Initializing TaskQueue...")
    task_queue = TaskQueue()
    await task_queue.initialize()

    # Store task_queue in app state for access in endpoints
    app.state.task_queue = task_queue
    logger.info("TaskQueue initialized.")

    yield

    # Shutdown
    logger.info("Application shutdown: Shutting down TaskQueue components...")
    await task_queue.shutdown(force=True)
    logger.info("TaskQueue components shut down.")
