# Использование клиента summarize_files.py с аутентификацией

Этот документ описывает, как использовать обновленный клиент `client/summarize_files.py` с поддержкой WebSocket аутентификации.

## Обзор изменений

Клиент теперь поддерживает:
- ✅ Аргумент `--api-key` для передачи API ключа
- ✅ Автоматическое добавление токена к WebSocket URL
- ✅ Улучшенная обработка ошибок аутентификации
- ✅ Информативные сообщения об ошибках

## Использование

### 1. Б<PERSON><PERSON> аутентификации (если REQUIRE_AUTH=false)

```bash
# Обычное использование без API ключа
python client/summarize_files.py /path/to/text/files

# С дополнительными параметрами
python client/summarize_files.py /path/to/text/files --mode concise --debug
```

### 2. С аутентификацией (если REQUIRE_AUTH=true)

```bash
# Использование с API ключом
python client/summarize_files.py /path/to/text/files --api-key ak_your_api_key_here

# С дополнительными параметрами
python client/summarize_files.py /path/to/text/files \
    --api-key ak_your_api_key_here \
    --mode detailed \
    --format \
    --debug
```

### 3. Тестирование всех режимов с аутентификацией

```bash
# Обработка файлов во всех доступных режимах
python client/summarize_files.py /path/to/text/files \
    --api-key ak_your_api_key_here \
    --totaltest \
    --format
```

## Получение API ключа

### Демо ключи (для разработки)

При первом запуске сервера автоматически создаются демо ключи:

```bash
# Демо ключи
DEMO_KEY=demo_read_key_12345     # только read права
ADMIN_KEY=admin_key_67890        # admin права (включает read, write)
```

Для WebSocket требуются права `write`, поэтому используйте admin ключ:

```bash
python client/summarize_files.py /path/to/text/files --api-key admin_key_67890
```

### Создание нового API ключа

```bash
# Создать новый API ключ через REST API
curl -X POST "http://localhost:8000/api/auth/keys" \
  -H "Authorization: Bearer admin_key_67890" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Summarize Client",
    "permissions": ["read", "write"],
    "rate_limit": 1000
  }'
```

## Примеры использования

### Пример 1: Базовое использование с аутентификацией

```bash
# Создать тестовые файлы
mkdir test_files
echo "Это тестовый текст для суммаризации. Он содержит несколько предложений." > test_files/test1.txt
echo "Другой тестовый файл с более длинным содержимым для проверки работы системы." > test_files/test2.txt

# Запустить обработку с аутентификацией
python client/summarize_files.py test_files --api-key admin_key_67890 --debug
```

**Вывод с аутентификацией:**
```
🔐 WebSocket аутентификация включена (API ключ: admin_key_***)
✅ Сервер уже запущен и доступен
📁 Обработка файлов...
```

**Вывод без аутентификации:**
```
🔓 WebSocket аутентификация отключена (подключение без API ключа)
✅ Сервер уже запущен и доступен
📁 Обработка файлов...
```

### Пример 2: Обработка с форматированием

```bash
# Обработка с форматированием Markdown
python client/summarize_files.py test_files \
    --api-key admin_key_67890 \
    --mode detailed \
    --format \
    --debug
```

### Пример 3: Тестирование всех режимов

```bash
# Обработка во всех доступных режимах
python client/summarize_files.py test_files \
    --api-key admin_key_67890 \
    --totaltest \
    --format
```

## Обработка ошибок

### Ошибки аутентификации

Клиент теперь распознает и обрабатывает ошибки аутентификации:

```
❌ Ошибка аутентификации: Неверный API ключ или недостаточно прав
💡 Проверьте правильность API ключа и наличие прав 'write'
```

### Отсутствие API ключа

Если сервер требует аутентификацию, но ключ не предоставлен:

```
❌ Ошибка аутентификации: API key required for WebSocket connection
💡 Возможно, требуется API ключ. Используйте --api-key
```

### Недостаточно прав

Если API ключ не имеет прав `write`:

```
❌ Ошибка аутентификации: Missing required permissions: ['write']
💡 Проверьте правильность API ключа и наличие прав 'write'
```

## Отладка

### Включение отладочных логов

```bash
# Включить подробные логи
python client/summarize_files.py test_files \
    --api-key admin_key_67890 \
    --debug
```

Это покажет:
- Конфигурацию WebSocket URL с аутентификацией
- Детали подключения и аутентификации
- Обмен сообщениями с сервером
- Подробности ошибок

### Проверка конфигурации сервера

```bash
# Проверить статус сервера
curl http://localhost:8000/health

# Проверить информацию о пользователе
curl -H "Authorization: Bearer admin_key_67890" \
     http://localhost:8000/api/auth/me
```

## Переменные окружения

Клиент также может читать API ключ из переменной окружения:

```bash
# Установить API ключ в переменную окружения
export WEBSOCKET_API_KEY=admin_key_67890

# Модифицировать клиент для чтения из переменной окружения (опционально)
```

## Миграция существующих скриптов

Для обновления существующих скриптов:

1. **Добавьте параметр `--api-key`** к вызовам клиента
2. **Обновите обработку ошибок** для распознавания ошибок аутентификации
3. **Тестируйте с демо ключами** перед использованием в production

### Пример миграции

```bash
# Было:
python client/summarize_files.py /data/texts --mode concise

# Стало:
python client/summarize_files.py /data/texts \
    --mode concise \
    --api-key admin_key_67890
```

## Безопасность

### Рекомендации

1. **Не включайте API ключи в код** - используйте аргументы командной строки или переменные окружения
2. **Используйте разные ключи** для разных приложений
3. **Регулярно обновляйте ключи** в production
4. **Мониторьте использование** через логи сервера

### Пример безопасного использования

```bash
# Читать API ключ из файла
API_KEY=$(cat ~/.config/yt-subs-api/api-key)
python client/summarize_files.py /data/texts --api-key "$API_KEY"

# Или из переменной окружения
export YT_SUBS_API_KEY=your_api_key_here
python client/summarize_files.py /data/texts --api-key "$YT_SUBS_API_KEY"
```

## Troubleshooting

### Частые проблемы

1. **Клиент не может подключиться**
   - Проверьте, что сервер запущен: `curl http://localhost:8000/health`
   - Проверьте правильность API ключа

2. **Ошибка "Invalid API key"**
   - Убедитесь, что используете правильный ключ
   - Проверьте, что ключ активен: `curl -H "Authorization: Bearer your_key" http://localhost:8000/api/auth/me`

3. **Ошибка "Missing required permissions"**
   - WebSocket требует права `write`
   - Создайте новый ключ с правильными правами

4. **Сервер требует аутентификацию, но раньше работал без неё**
   - Проверьте настройку `REQUIRE_AUTH` в `.env`
   - Временно отключите: `REQUIRE_AUTH=false` (только для разработки)
