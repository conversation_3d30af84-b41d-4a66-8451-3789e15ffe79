# Аутентификация API

## Обзор

API использует систему аутентификации на основе API ключей. Каждый запрос к защищенным эндпойнтам должен включать действительный API ключ в заголовке Authorization.

## Получение API ключа

### Демо ключи (для разработки)

При первом запуске приложения автоматически создаются демо ключи:

- **Demo User**: `demo_key_12345`
  - Права: `read` (только чтение)
  - Лимит: 50 запросов в час

- **Admin User**: `admin_key_67890`
  - Права: `read`, `write`, `admin` (полные права)
  - Лимит: 1000 запросов в час

### Создание новых API ключей

Только пользователи с правами `admin` могут создавать новые API ключи:

```bash
curl -X POST "http://localhost:8000/api/auth/keys" \
  -H "Authorization: Bearer admin_key_67890" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New User",
    "permissions": ["read", "write"],
    "rate_limit": 100
  }'
```

## Использование API ключей

### Заголовок Authorization

Включите API ключ в заголовок Authorization с префиксом "Bearer":

```bash
curl -X POST "http://localhost:8000/api/subtitles" \
  -H "Authorization: Bearer your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=example"
  }'
```

### Примеры для разных языков

#### Python (requests)

```python
import requests

headers = {
    'Authorization': 'Bearer your_api_key_here',
    'Content-Type': 'application/json'
}

response = requests.post(
    'http://localhost:8000/api/subtitles',
    headers=headers,
    json={
        'url': 'https://www.youtube.com/watch?v=example'
    }
)
```

#### JavaScript (fetch)

```javascript
const response = await fetch('http://localhost:8000/api/subtitles', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_key_here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    url: 'https://www.youtube.com/watch?v=example'
  })
});
```

## Deprecated: client_uid

**ВАЖНО**: Поле `client_uid` больше не используется в обработке запросов и считается устаревшим.

- ✅ **Новые клиенты**: Не отправляйте поле `client_uid` в запросах
- ✅ **Старые клиенты**: Поле `client_uid` принимается для обратной совместимости, но игнорируется
- 🔐 **Аутентификация**: Используйте API ключи вместо `client_uid` для идентификации

### Миграция с client_uid

**Старый способ (deprecated):**
```json
{
  "url": "https://www.youtube.com/watch?v=example",
  "client_uid": "user123"
}
```

**Новый способ (рекомендуется):**
```json
{
  "url": "https://www.youtube.com/watch?v=example"
}
```

## Права доступа

### Уровни прав

1. **read** - Чтение данных
   - Получение статуса задач (`GET /api/task/{task_id}`)
   - Просмотр метрик (`GET /metrics`)
   - Информация о пользователе (`GET /api/auth/me`)

2. **write** - Создание и изменение данных
   - Создание задач извлечения субтитров (`POST /api/subtitles`)
   - Создание задач суммаризации (`POST /api/summarize`)
   - Все права уровня `read`

3. **admin** - Административные функции
   - Создание API ключей (`POST /api/auth/keys`)
   - Просмотр всех API ключей (`GET /api/auth/keys`)
   - Отзыв API ключей (`DELETE /api/auth/keys/{key}`)
   - Все права уровней `read` и `write`

### Проверка прав

Проверить свои права можно через эндпойнт:

```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer your_api_key_here"
```

## Управление API ключами

### Просмотр информации о пользователе

```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer your_api_key_here"
```

### Список всех API ключей (только admin)

```bash
curl -X GET "http://localhost:8000/api/auth/keys" \
  -H "Authorization: Bearer admin_key_67890"
```

### Создание нового API ключа (только admin)

```bash
curl -X POST "http://localhost:8000/api/auth/keys" \
  -H "Authorization: Bearer admin_key_67890" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Production User",
    "permissions": ["read", "write"],
    "rate_limit": 500
  }'
```

### Отзыв API ключа (только admin)

```bash
curl -X DELETE "http://localhost:8000/api/auth/keys/demo_key_1..." \
  -H "Authorization: Bearer admin_key_67890"
```

## Обработка ошибок аутентификации

### Коды ошибок

- **401 Unauthorized** - Отсутствует или недействительный API ключ
- **403 Forbidden** - Недостаточно прав для выполнения операции

### Примеры ответов

#### Отсутствует API ключ

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "API key required",
    "error_id": "uuid-here"
  }
}
```

#### Недействительный API ключ

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid API key",
    "error_id": "uuid-here"
  }
}
```

#### Недостаточно прав

```json
{
  "error": {
    "code": "FORBIDDEN",
    "message": "Missing required permissions: ['admin']",
    "error_id": "uuid-here"
  }
}
```

## Безопасность

### Рекомендации

1. **Храните API ключи в безопасности** - Никогда не включайте их в код или публичные репозитории
2. **Используйте переменные окружения** - Храните ключи в `.env` файлах или переменных окружения
3. **Ротация ключей** - Регулярно обновляйте API ключи
4. **Минимальные права** - Предоставляйте только необходимые права
5. **Мониторинг** - Отслеживайте использование API ключей

### Переменные окружения

```bash
# .env файл
API_KEY=your_production_api_key_here
```

### Отключение аутентификации (только для разработки)

Для отключения аутентификации в режиме разработки:

```bash
# .env файл
REQUIRE_AUTH=false
```

**⚠️ Внимание**: Никогда не отключайте аутентификацию в production!

## WebSocket Аутентификация

WebSocket эндпойнты теперь также требуют аутентификации:

- **`/ws/subtitles`** - требует разрешение `write`
- **`/ws/summarize`** - требует разрешение `write`

### Методы аутентификации для WebSocket

1. **Query parameter** (рекомендуется):
   ```javascript
   const ws = new WebSocket('ws://localhost:8000/ws/subtitles?token=ak_your_api_key');
   ```

2. **Authorization header**:
   ```javascript
   const ws = new WebSocket('ws://localhost:8000/ws/subtitles', [], {
       headers: { 'Authorization': 'Bearer ak_your_api_key' }
   });
   ```

3. **Subprotocol**:
   ```javascript
   const ws = new WebSocket('ws://localhost:8000/ws/subtitles', ['api_key_ak_your_api_key']);
   ```

Подробная документация: [WebSocket Authentication](WEBSOCKET_AUTHENTICATION.md)

## Лимиты запросов

Каждый API ключ имеет лимит запросов в час. При превышении лимита API вернет ошибку 429 (Too Many Requests).

Текущие лимиты можно посмотреть через:

```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer your_api_key_here"
```

## Поддержка

При возникновении проблем с аутентификацией:

1. Проверьте правильность API ключа
2. Убедитесь, что у вас есть необходимые права
3. Проверьте лимиты запросов
4. Обратитесь к администратору для создания нового ключа
