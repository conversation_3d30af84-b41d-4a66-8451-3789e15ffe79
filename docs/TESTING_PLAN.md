# План тестирования для FastAPI YouTube Subtitles API

## Обзор проекта

Проект представляет собой FastAPI сервер для извлечения и суммаризации субтитров YouTube видео с следующими основными компонентами:

### Основные функции:
- **Извлечение субтитров** из YouTube видео (английские и русские)
- **Суммаризация текста** с использованием Gemini AI
- **Конвертация TTML в TXT** формат
- **Система аутентификации** с API ключами
- **WebSocket поддержка** для real-time обновлений
- **Система очередей** для обработки задач
- **Rate limiting** и мониторинг

### Архитектура:
- **REST API** endpoints (`/api/subtitles`, `/api/summarize`, `/api/tasks`, `/api/auth`)
- **WebSocket** endpoints (`/ws/subtitles`, `/ws/summarize`)
- **Система очередей** с приоритизацией задач
- **База данных** (SQLite/PostgreSQL) для хранения результатов
- **Middleware** для аутентификации, логирования, rate limiting

## План тестирования

### 1. Модульные тесты (Unit Tests)

#### 1.1 Тесты моделей и валидации (`test_models.py`)
- ✅ **Схемы запросов/ответов** (SubtitleRequest, SummarizeRequest, etc.)
- ✅ **Валидация данных** (YouTube URLs, client_uid, text content)
- ✅ **Аутентификация схемы** (APIKeyRequest, UserInfo)
- ✅ **Файловые схемы** (FileUploadRequest, FileMetadata)

#### 1.2 Тесты конфигурации (`test_config.py`)
- ✅ **Загрузка настроек** из environment variables
- ✅ **Валидация конфигурации** (обязательные поля, типы данных)
- ✅ **CORS настройки** и безопасность
- ✅ **Rate limiting конфигурация**

#### 1.3 Тесты сервисов (`test_services.py`)
- ✅ **SubtitleService** - извлечение субтитров
- ✅ **SummarizeService** - суммаризация текста
- ✅ **ConverterService** - конвертация TTML в TXT
- ✅ **TaskService** - управление задачами

#### 1.4 Тесты утилит (`test_utils.py`)
- ✅ **Logger setup** и конфигурация
- ✅ **ProxyManager** для SOCKS5 прокси
- ✅ **WebSocket manager** функциональность

### 2. Интеграционные тесты (Integration Tests)

#### 2.1 Тесты API endpoints (`test_api.py`)
- ✅ **Health check** endpoint (`/health`, `/ping`)
- ✅ **Subtitles endpoints** (`POST /api/subtitles`, `POST /api/subtitles/get_id`)
- ✅ **Summarize endpoints** (`POST /api/summarize`, `POST /api/summarize/file`)
- ✅ **Tasks endpoint** (`GET /api/task/{task_id}`)
- ✅ **Converter endpoint** (`POST /api/convert/ttml-to-txt`)

#### 2.2 Тесты аутентификации (`test_auth.py`)
- ✅ **API key validation** и permissions
- ✅ **Auth middleware** функциональность
- ✅ **Rate limiting** по пользователям
- ✅ **Admin endpoints** для управления ключами

#### 2.3 Тесты WebSocket (`test_websockets.py`)
- ✅ **WebSocket connections** и аутентификация
- ✅ **Real-time subtitle extraction** через WebSocket
- ✅ **Real-time summarization** через WebSocket
- ✅ **Error handling** в WebSocket соединениях

#### 2.4 Тесты базы данных (`test_database.py`)
- ✅ **Database connections** (SQLite и PostgreSQL)
- ✅ **CRUD operations** для субтитров и задач
- ✅ **Connection pooling** и производительность
- ✅ **Database migrations** и схема

### 3. Функциональные тесты (Functional Tests)

#### 3.1 Тесты системы очередей (`test_task_queue.py`)
- ✅ **Task queue management** и приоритизация
- ✅ **Worker management** для разных типов задач
- ✅ **Rate limiting** в очередях
- ✅ **Task timeout** и error handling

#### 3.2 Тесты middleware (`test_middleware.py`)
- ✅ **Error handling middleware** и форматирование ошибок
- ✅ **Logging middleware** и structured logging
- ✅ **Security headers** middleware
- ✅ **Metrics collection** middleware

### 4. End-to-End тесты (E2E Tests)

#### 4.1 Полный workflow тесты (`test_e2e.py`)
- ✅ **Полный цикл извлечения субтитров** (от URL до результата)
- ✅ **Полный цикл суммаризации** (от текста до результата)
- ✅ **WebSocket real-time обновления**
- ✅ **Error scenarios** и recovery

### 5. Тесты производительности (`test_performance.py`)
- ✅ **Load testing** для API endpoints
- ✅ **Concurrent requests** handling
- ✅ **Memory usage** и resource management
- ✅ **Rate limiting** эффективность

## Структура тестов

```
tests/
├── conftest.py                 # Pytest fixtures и конфигурация
├── unit/
│   ├── test_models.py         # Тесты моделей и валидации
│   ├── test_config.py         # Тесты конфигурации
│   ├── test_services.py       # Тесты сервисов
│   └── test_utils.py          # Тесты утилит
├── integration/
│   ├── test_api.py            # Тесты API endpoints
│   ├── test_auth.py           # Тесты аутентификации
│   ├── test_websockets.py     # Тесты WebSocket
│   └── test_database.py       # Тесты базы данных
├── functional/
│   ├── test_task_queue.py     # Тесты системы очередей
│   └── test_middleware.py     # Тесты middleware
├── e2e/
│   └── test_e2e.py            # End-to-end тесты
└── performance/
    └── test_performance.py    # Тесты производительности
```

## Запуск тестов

```bash
# Все тесты
uv run python -m pytest

# Только unit тесты
uv run python -m pytest tests/unit/

# Только integration тесты
uv run python -m pytest tests/integration/

# С покрытием кода
uv run python -m pytest --cov=. --cov-report=html

# Параллельный запуск
uv run python -m pytest -n auto
```

## Приоритеты реализации

### Фаза 1: Базовые тесты (Высокий приоритет)
1. ✅ `conftest.py` - базовые fixtures
2. ✅ `test_models.py` - валидация данных
3. ✅ `test_config.py` - конфигурация
4. ✅ `test_api.py` - основные API endpoints

### Фаза 2: Интеграционные тесты (Средний приоритет)
1. ✅ `test_auth.py` - аутентификация
2. ✅ `test_database.py` - база данных
3. ✅ `test_websockets.py` - WebSocket функциональность

### Фаза 3: Расширенные тесты (Низкий приоритет)
1. ✅ `test_task_queue.py` - система очередей
2. ✅ `test_e2e.py` - end-to-end сценарии
3. ✅ `test_performance.py` - производительность

## Тестовые данные

- **Mock YouTube URLs** для тестирования
- **Sample text content** для суммаризации
- **Test API keys** с разными permissions
- **Mock external API responses** (Gemini AI)
- **Test database** с изолированными данными
