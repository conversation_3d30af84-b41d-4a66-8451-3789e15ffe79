# WebSocket Authentication

Эта документация описывает систему аутентификации для WebSocket соединений в YouTube Subtitles API.

## Обзор

WebSocket эндпойнты теперь требуют аутентификации через API ключи, аналогично REST API. Аутентификация происходит **на этапе подключения** к WebSocket, что обеспечивает безопасность и производительность.

## Настройка аутентификации

### Включение/отключение аутентификации

Аутентификация контролируется переменной окружения `REQUIRE_AUTH`:

```bash
# .env файл
REQUIRE_AUTH=true   # Аутентификация включена (по умолчанию)
REQUIRE_AUTH=false  # Аутентификация отключена (только для разработки)
```

**⚠️ Внимание**: Никогда не отключайте аутентификацию в production!

## Методы аутентификации

WebSocket поддерживает три метода передачи API ключа:

### 1. Query Parameter (Рекомендуется)

Передача API ключа через query параметр `token`:

```javascript
const websocket = new WebSocket('ws://localhost:8000/ws/subtitles?token=ak_your_api_key_here');
```

**Преимущества:**
- Простота реализации
- Поддерживается всеми WebSocket клиентами
- Хорошо работает в браузерах

### 2. Authorization Header

Передача API ключа через заголовок Authorization:

```javascript
const websocket = new WebSocket('ws://localhost:8000/ws/subtitles', [], {
    headers: {
        'Authorization': 'Bearer ak_your_api_key_here'
    }
});
```

**Примечание:** Не все WebSocket клиенты поддерживают кастомные заголовки.

### 3. Subprotocol

Передача API ключа через WebSocket subprotocol:

```javascript
const websocket = new WebSocket('ws://localhost:8000/ws/subtitles', ['api_key_ak_your_api_key_here']);
```

**Использование:** Для специализированных клиентов.

## Требования к разрешениям

### WebSocket эндпойнты

- **`/ws/subtitles`** - требует разрешение `write`
- **`/ws/summarize`** - требует разрешение `write`

### Типы разрешений

- **`read`** - чтение данных
- **`write`** - создание и изменение данных (требуется для WebSocket)
- **`admin`** - управление API ключами и системными настройками

## Примеры использования

### JavaScript (Browser)

```javascript
// Метод 1: Query parameter (рекомендуется)
const apiKey = 'ak_your_api_key_here';
const websocket = new WebSocket(`ws://localhost:8000/ws/subtitles?token=${apiKey}`);

websocket.onopen = function(event) {
    console.log('WebSocket connected and authenticated');
    
    // Отправить URL для обработки
    websocket.send(JSON.stringify({
        url: 'https://www.youtube.com/watch?v=VIDEO_ID'
    }));
};

websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};

websocket.onerror = function(error) {
    console.error('WebSocket error:', error);
};

websocket.onclose = function(event) {
    if (event.code === 4001) {
        console.error('Authentication failed:', event.reason);
    } else if (event.code === 4000) {
        console.error('Authentication error:', event.reason);
    }
};
```

### Python (websockets library)

```python
import asyncio
import websockets
import json

async def connect_websocket():
    api_key = "ak_your_api_key_here"
    uri = f"ws://localhost:8000/ws/subtitles?token={api_key}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("WebSocket connected and authenticated")
            
            # Отправить URL для обработки
            await websocket.send(json.dumps({
                "url": "https://www.youtube.com/watch?v=VIDEO_ID"
            }))
            
            # Получать сообщения
            async for message in websocket:
                data = json.loads(message)
                print(f"Received: {data}")
                
    except websockets.exceptions.ConnectionClosedError as e:
        if e.code == 4001:
            print(f"Authentication failed: {e.reason}")
        elif e.code == 4000:
            print(f"Authentication error: {e.reason}")
        else:
            print(f"Connection closed: {e}")

# Запуск
asyncio.run(connect_websocket())
```

### Node.js (ws library)

```javascript
const WebSocket = require('ws');

const apiKey = 'ak_your_api_key_here';
const ws = new WebSocket(`ws://localhost:8000/ws/subtitles?token=${apiKey}`);

ws.on('open', function open() {
    console.log('WebSocket connected and authenticated');
    
    // Отправить URL для обработки
    ws.send(JSON.stringify({
        url: 'https://www.youtube.com/watch?v=VIDEO_ID'
    }));
});

ws.on('message', function message(data) {
    const parsed = JSON.parse(data);
    console.log('Received:', parsed);
});

ws.on('error', function error(err) {
    console.error('WebSocket error:', err);
});

ws.on('close', function close(code, reason) {
    if (code === 4001) {
        console.error('Authentication failed:', reason.toString());
    } else if (code === 4000) {
        console.error('Authentication error:', reason.toString());
    }
});
```

## Коды ошибок WebSocket

| Код | Описание | Причина |
|-----|----------|---------|
| 4000 | Authentication Error | Общая ошибка аутентификации |
| 4001 | Authentication Failed | Неверный API ключ или недостаточно прав |
| 1000 | Normal Closure | Нормальное закрытие соединения |

## Получение API ключей

### Через REST API

```bash
# Создать новый API ключ (требует admin права)
curl -X POST "http://localhost:8000/api/auth/keys" \
  -H "Authorization: Bearer your_admin_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My WebSocket Client",
    "permissions": ["read", "write"],
    "rate_limit": 1000
  }'
```

### Демо ключи

При первом запуске автоматически создаются демо ключи:

```bash
# Демо ключи (только для разработки)
DEMO_KEY=demo_read_key_12345     # read права
ADMIN_KEY=admin_key_67890        # admin права (включает read, write)
```

## Безопасность

### Рекомендации

1. **Используйте HTTPS/WSS в production**
   ```javascript
   const websocket = new WebSocket('wss://yourdomain.com/ws/subtitles?token=...');
   ```

2. **Храните API ключи безопасно**
   - Не включайте в код
   - Используйте переменные окружения
   - Регулярно обновляйте

3. **Минимальные права**
   - Предоставляйте только необходимые разрешения
   - Используйте отдельные ключи для разных приложений

4. **Мониторинг**
   - Отслеживайте использование API ключей
   - Проверяйте логи на подозрительную активность

### Логирование

Все попытки аутентификации логируются:

```
# Успешная аутентификация
INFO: WebSocket /ws/subtitles authenticated: MyApp (method: query_param) from 127.0.0.1:12345

# Неудачная аутентификация
WARNING: WebSocket /ws/subtitles authentication failed from 127.0.0.1:12345: Invalid API key
```

## Миграция с неаутентифицированных WebSocket

### Для существующих клиентов

1. **Получите API ключ** через REST API или используйте демо ключи
2. **Обновите код клиента** для передачи API ключа
3. **Тестируйте подключение** с новой аутентификацией

### Временное отключение (только для разработки)

```bash
# .env файл
REQUIRE_AUTH=false
```

Это позволит существующим клиентам работать без изменений во время миграции.

## Troubleshooting

### Частые проблемы

1. **WebSocket закрывается сразу после подключения**
   - Проверьте правильность API ключа
   - Убедитесь, что ключ имеет права `write`

2. **Ошибка "API key required"**
   - API ключ не передан или передан неправильно
   - Проверьте метод передачи ключа

3. **Ошибка "Missing required permissions"**
   - API ключ не имеет прав `write`
   - Обновите права ключа или создайте новый

### Отладка

Включите DEBUG логирование для подробной информации:

```bash
# .env файл
LOG_LEVEL=DEBUG
```

Это покажет детальную информацию об аутентификации в логах.
