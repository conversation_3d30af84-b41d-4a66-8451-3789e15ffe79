[tool:pytest]
# Pytest configuration file

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 8.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    functional: Functional tests
    e2e: End-to-end tests
    performance: Performance tests
    slow: Slow running tests
    auth: Tests requiring authentication
    websocket: WebSocket tests
    database: Database tests
    external: Tests requiring external services

# Asyncio configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Test timeout (in seconds)
timeout = 300

# Coverage configuration
# (if pytest-cov is installed)
# --cov=.
# --cov-report=html
# --cov-report=term-missing
# --cov-fail-under=80

# Parallel execution
# (if pytest-xdist is installed)
# -n auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:websockets.*
    ignore::UserWarning:uvicorn.*
