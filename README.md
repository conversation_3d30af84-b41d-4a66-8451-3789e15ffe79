# YouTube Subtitles and Text Summarization API

API сервис для скачивания субтитров с YouTube видео на русском и английском языках и суммаризации текста с помощью Gemini AI.

## Требования

- Python 3.12+
- uv (для управления зависимостями)

## Установка

1. Клонируйте репозиторий:
```bash
git clone <repository-url>
cd yt_subs_api4
```

2. Установите зависимости с помощью uv:
```bash
uv sync
```

3. Создайте `.env` файл и настройте параметры:
```bash
cp .env.example .env
```

## Конфигурация

В файле `.env` можно настроить:

### Основные настройки
- `HOST` - хост для API (по умолчанию 0.0.0.0)
- `PORT` - порт для API (по умолчанию 8000)
- `RELOAD` - автоматическая перезагрузка при изменении кода (по умолчанию false)
- `SOCKS5_PROXY` - опциональный SOCKS5 прокси (формат: socks5://host:port)
- `GEM_API` - ключ для Google Gemini LLM для задачи суммаризации текста

### Настройки логирования
- `LOG_LEVEL` - уровень логирования: DEBUG, INFO, WARNING, ERROR, CRITICAL (по умолчанию INFO)
- `STRUCTURED_LOGGING` - использовать структурированное логирование (по умолчанию true)
- `LOG_JSON_FORMAT` - использовать JSON формат для логов (по умолчанию false)

## Запуск

### Обычный запуск
```bash
uv run main.py
```

### Режим разработки
```bash
# Через .env файл (установите RELOAD=true в .env)
uv run main.py

# Или через командную строку
uv run main.py --reload

# С отладочным логированием
uv run main.py --reload --log-level DEBUG
```

### Доступные параметры командной строки
- `--reload` - включить автоматическую перезагрузку
- `--log-level` - установить уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `--json-logs` - использовать JSON формат для логов

API будет доступно по адресу http://localhost:8000

## Использование API

### REST API

#### Создание задачи на скачивание субтитров

```bash
curl -X POST "http://localhost:8000/subtitles" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.youtube.com/watch?v=VIDEO_ID"}'
```

Ответ для задачи получения субтитров будет содержать:
```json
{
    "task_id": "TASK_ID",
    "status": "completed",
    "title": "Title of video",
    "en_subtitles": "English ...",
    "ru_subtitles": "Russian ...",
    "error": null
}
```

#### Отправка текста на суммаризацию

```bash
curl -X POST "http://localhost:8000/summarize" \
     -H "Content-Type: application/json" \
     -d '{"og_text": "Ваш длинный текст для суммаризации..."}'
```

#### Проверка статуса и получение результата любой задачи

```bash
# ID задачи возвращается при создании задачи
curl "http://localhost:8000/task/TASK_ID"
```

Ответ для задачи суммаризации будет содержать:
```json
{
    "task_id": "TASK_ID",
    "status": "completed",
    "summary": "Суммаризированный текст...",
    "error": null
}
```

### WebSocket API

#### 1. Обработка субтитров

WebSocket API позволяет получать обновления о статусе обработки субтитров в реальном времени.

##### JavaScript пример:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/subtitles');

ws.onopen = () => {
    // Отправка URL для обработки
    ws.send(JSON.stringify({
        url: 'https://www.youtube.com/watch?v=VIDEO_ID'
    }));
};

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);

    if (data.type === 'status_update') {
        console.log('Статус задачи:', data.status);
    } else if (data.type === 'result') {
        console.log('Заголовок видео:', data.title);
        console.log('Русские субтитры:', data.ru_subtitles);
        console.log('Английские субтитры:', data.en_subtitles);
    }

    if (data.error) {
        console.error('Ошибка:', data.error);
    }
};

ws.onerror = (error) => {
    console.error('WebSocket error:', error);
};

ws.onclose = () => {
    console.log('WebSocket connection closed');
};
```

##### Python пример:
```python
import asyncio
import websockets
import json

async def get_subtitles():
    async with websockets.connect('ws://localhost:8000/ws/subtitles') as ws:
        # Отправка URL для обработки
        await ws.send(json.dumps({
            'url': 'https://www.youtube.com/watch?v=VIDEO_ID'
        }))

        # Получение обновлений
        while True:
            response = await ws.recv()
            data = json.loads(response)

            if data['type'] == 'status_update':
                print(f"Статус задачи: {data['status']}")
            elif data['type'] == 'result':
                print(f"Заголовок видео: {data['title']}")
                print(f"Русские субтитры: {data['ru_subtitles']}")
                print(f"Английские субтитры: {data['en_subtitles']}")

            if data['error']:
                print(f"Ошибка: {data['error']}")

            # Если задача завершена или произошла ошибка, завершаем работу
            if data['status'] in ['completed', 'failed']:
                break

asyncio.run(get_subtitles())
```

#### 2. Суммаризация текста

WebSocket API также поддерживает суммаризацию текста и файлов в реальном времени.

##### JavaScript пример:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/summarize');

// Для отправки текста
ws.onopen = () => {
    ws.send(JSON.stringify({
        type: 'text_input',
        content: 'Ваш текст для суммаризации...'
    }));
};

// Для отправки файла
function sendFile(file) {
    const reader = new FileReader();
    reader.onload = () => {
        // Конвертируем содержимое файла в base64
        const base64Content = reader.result.split(',')[1];
        ws.send(JSON.stringify({
            type: 'file_input',
            content: base64Content,
            filename: file.name
        }));
    };
    reader.readAsDataURL(file);
}

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);

    if (data.type === 'status_update') {
        console.log('Статус задачи:', data.status);
    } else if (data.type === 'result') {
        console.log('Суммаризация:', data.summary);
    }

    if (data.error) {
        console.error('Ошибка:', data.error);
    }
};
```

##### Python пример:
```python
import asyncio
import websockets
import json
import base64

async def summarize_text(text):
    async with websockets.connect('ws://localhost:8000/ws/summarize') as ws:
        # Отправка текста для суммаризации
        await ws.send(json.dumps({
            'type': 'text_input',
            'content': text
        }))

        while True:
            response = await ws.recv()
            data = json.loads(response)

            if data['type'] == 'status_update':
                print(f"Статус задачи: {data['status']}")
            elif data['type'] == 'result':
                print(f"Суммаризация: {data['summary']}")

            if data['error']:
                print(f"Ошибка: {data['error']}")

            if data['status'] in ['completed', 'failed']:
                break

async def summarize_file(file_path):
    async with websockets.connect('ws://localhost:8000/ws/summarize') as ws:
        # Читаем и кодируем файл в base64
        with open(file_path, 'rb') as f:
            content = base64.b64encode(f.read()).decode('utf-8')

        # Отправляем файл для суммаризации
        await ws.send(json.dumps({
            'type': 'file_input',
            'content': content,
            'filename': file_path.split('/')[-1]
        }))

        while True:
            response = await ws.recv()
            data = json.loads(response)

            if data['type'] == 'status_update':
                print(f"Статус задачи: {data['status']}")
            elif data['type'] == 'result':
                print(f"Суммаризация: {data['summary']}")

            if data['error']:
                print(f"Ошибка: {data['error']}")

            if data['status'] in ['completed', 'failed']:
                break

# Пример использования
asyncio.run(summarize_text("Текст для суммаризации..."))
# или
asyncio.run(summarize_file("path/to/your/file.txt"))
```

## API Endpoints

### REST API (требует аутентификацию)
- `POST /api/subtitles` - создать новую задачу на скачивание субтитров
- `POST /api/summarize` - создать новую задачу на суммаризацию текста
- `POST /api/summarize/file` - создать новую задачу на суммаризацию файла
- `GET /api/task/{task_id}` - получить статус задачи и результат
- `GET /api/auth/me` - информация о текущем пользователе
- `POST /api/auth/keys` - создать новый API ключ (admin)

### WebSocket (требует аутентификацию)
- `WebSocket /ws/subtitles` - WebSocket эндпоинт для получения обновлений о субтитрах в реальном времени
- `WebSocket /ws/summarize` - WebSocket эндпоинт для суммаризации текста и файлов в реальном времени

### Аутентификация
Все эндпойнты требуют API ключ. WebSocket поддерживает несколько методов аутентификации:
```javascript
// Query parameter (рекомендуется)
const ws = new WebSocket('ws://localhost:8000/ws/subtitles?token=your_api_key');

// Authorization header
const ws = new WebSocket('ws://localhost:8000/ws/subtitles', [], {
    headers: { 'Authorization': 'Bearer your_api_key' }
});
```

Подробнее: [Документация по аутентификации](docs/AUTHENTICATION.md)

### Клиент с аутентификацией
Обновленный клиент `client/summarize_files.py` поддерживает WebSocket аутентификацию:

```bash
# Использование с API ключом
python client/summarize_files.py /path/to/files --api-key admin_key_67890

# Без аутентификации (если REQUIRE_AUTH=false)
python client/summarize_files.py /path/to/files
```

Подробнее: [Использование клиента с аутентификацией](examples/client_usage_with_auth.md)

## Логирование

Логи сохраняются в файл `app.log` в корневой директории проекта с автоматической ротацией.

### Настройка уровня логирования

Уровень логирования настраивается через `.env` файл:
```bash
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

Или через командную строку:
```bash
uv run main.py --log-level DEBUG
```

### Подробная документация

Подробную информацию о настройке логирования и режиме разработки см. в [docs/LOGGING_AND_DEVELOPMENT.md](docs/LOGGING_AND_DEVELOPMENT.md).