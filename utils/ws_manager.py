from __future__ import annotations

import asyncio
import time
from typing import TYPE_CHECKING

from fastapi import WebSocket
from loguru import logger

if TYPE_CHECKING:
    from worker.queue import TaskQueue  # Import TaskQueue for type hinting

PING_INTERVAL = 30  # Server sends pings at this interval (seconds)
CLIENT_TIMEOUT_THRESHOLD = (
    PING_INTERVAL * 2
)  # Client considered timed out if no pong after this duration


class ConnectionManager:
    def __init__(self, ping_interval: int = PING_INTERVAL):
        # task_id -> Set[WebSocket]
        self.active_connections: dict[str, set[WebSocket]] = {}
        # task_id -> Dict[WebSocket, float] (last_pong_time)
        self.client_responses: dict[str, dict[WebSocket, float]] = {}
        # task_id -> Set[WebSocket] для отслеживания подписчиков задачи
        self.task_subscribers: dict[str, set[WebSocket]] = {}
        self.ping_interval = (
            ping_interval  # Used as heartbeat_interval in _cleanup_stale_connections
        )
        self.cleanup_task: asyncio.Task | None = None
        self.task_queue_ref: TaskQueue | None = None

    def set_task_queue_reference(self, task_queue: TaskQueue):
        """Sets the reference to the TaskQueue instance."""
        self.task_queue_ref = task_queue

    async def start_cleanup_task(self):
        """Start the background task that cleans up stale connections"""
        if self.cleanup_task is None or self.cleanup_task.done():
            self.cleanup_task = asyncio.create_task(self._cleanup_stale_connections())
            logger.info("Stale connection cleanup task started.")

    async def _cleanup_stale_connections(self):
        """Periodically check and remove stale connections and cancel tasks if needed."""
        logger.info(
            f"Cleanup task running. Checking every {self.ping_interval} seconds."
        )
        while True:
            await asyncio.sleep(self.ping_interval)
            current_time = time.time()

            # tasks_to_potentially_cancel = set()

            for task_id, websockets_map in list(self.client_responses.items()):
                # websockets_map is Dict[WebSocket, float]
                if (
                    not websockets_map
                ):  # No clients for this task_id in client_responses's map
                    # This task_id might still be in active_connections.
                    # _ensure_task_cleanup_if_no_clients will handle it later if active_connections is also empty.
                    continue

                timed_out_websockets_for_task = []
                for websocket, last_pong_time in list(websockets_map.items()):
                    if current_time - last_pong_time > CLIENT_TIMEOUT_THRESHOLD:
                        logger.warning(
                            f"Client for task {task_id} (WebSocket: {websocket}) timed out. Removing client connection."
                        )
                        timed_out_websockets_for_task.append(websocket)

                for websocket_to_disconnect in timed_out_websockets_for_task:
                    # Pass called_from_cleanup=True to avoid redundant closing if websocket is already dead
                    await self.disconnect_from_task(
                        task_id, websocket_to_disconnect, called_from_cleanup=True
                    )
                    # disconnect_from_task now calls _ensure_task_cleanup_if_no_clients,
                    # which will handle cancellation if this was the last client.

            # After processing timeouts, check all known tasks.
            # Some tasks might have no clients left or were never in client_responses but are in active_connections.
            all_known_task_ids = set(self.active_connections.keys()) | set(
                self.client_responses.keys()
            )
            # if (
            #     not all_known_task_ids
            #     and self.cleanup_task
            #     and not self.cleanup_task.done()
            # ):  # Check if any tasks are tracked at all
            #     logger.debug(
            #         "Cleanup task: No tasks currently tracked by ConnectionManager."
            #     )

            # Iterate over a copy
            for task_id_to_check in list(all_known_task_ids):
                await self._ensure_task_cleanup_if_no_clients(task_id_to_check)

    async def _ensure_task_cleanup_if_no_clients(self, task_id: str):
        """Checks if a task has any responsive or active clients and requests cancellation if not. Cleans up task_id from manager."""
        # Condition: No responsive clients AND no active (even if unresponsive) clients for this task.
        no_responsive_clients = not self.client_responses.get(task_id)
        no_active_connections = not self.active_connections.get(task_id)

        if no_responsive_clients and no_active_connections:
            if self.task_queue_ref and await self.task_queue_ref.is_task_active(
                task_id
            ):
                logger.info(
                    f"All clients for task {task_id} are gone. Requesting task cancellation."
                )
                await self.task_queue_ref.cancel_task(task_id)
            # Final cleanup from ConnectionManager's perspective
            self.active_connections.pop(task_id, None)
            self.client_responses.pop(task_id, None)
            logger.debug(
                f"Fully cleaned up task {task_id} from ConnectionManager after ensuring no clients."
            )

    async def connect(self, task_id: str, websocket: WebSocket):
        """Register a new WebSocket connection for a task."""
        if task_id not in self.active_connections:
            self.active_connections[task_id] = set()
        self.active_connections[task_id].add(websocket)

        if task_id not in self.client_responses:
            self.client_responses[task_id] = {}
        self.client_responses[task_id][websocket] = time.time()

        if task_id not in self.task_subscribers:
            self.task_subscribers[task_id] = set()
        self.task_subscribers[task_id].add(websocket)
        logger.debug(f"Registered WebSocket for task {task_id}: {websocket}")
        logger.debug(f"Added subscriber for task {task_id}: {websocket}")

    async def handle_pong(self, task_id: str, websocket: WebSocket):
        """Handles a pong response from a client for a specific task."""
        if (
            task_id in self.client_responses
            and websocket in self.client_responses[task_id]
        ):
            self.client_responses[task_id][websocket] = time.time()
            logger.debug(f"Pong received for task {task_id} from {websocket}")
        else:
            logger.warning(
                f"Received pong for untracked task/websocket: {task_id}/{websocket}"
            )

    # Removed update_heartbeat and send_heartbeat as they are obsolete.

    async def disconnect_websocket(
        self, websocket: WebSocket, called_from_cleanup: bool = False
    ):
        """Disconnect and cleanup a specific WebSocket connection from all tasks."""
        task_ids_affected = []
        # Iterate over a copy of task_ids from client_responses as it might change
        for task_id in list(self.client_responses.keys()):
            if websocket in self.client_responses.get(task_id, {}):
                task_ids_affected.append(task_id)
                await self.disconnect_from_task(
                    task_id, websocket, called_from_cleanup=True
                )  # Pass flag

        # Also check active_connections for any task_id where websocket might exist
        # even if not in client_responses (though ideally they should be in sync)
        for task_id in list(self.active_connections.keys()):
            if websocket in self.active_connections.get(task_id, set()):
                if task_id not in task_ids_affected:  # Avoid double processing
                    task_ids_affected.append(task_id)
                    await self.disconnect_from_task(
                        task_id, websocket, called_from_cleanup=True
                    )  # Pass flag

        if not task_ids_affected:
            logger.debug(
                f"WebSocket {websocket} not found in any active task client_responses for full disconnect."
            )

        # General close, only if not called from cleanup which handles its own disconnections.
        # disconnect_from_task will attempt to close the websocket too.
        if not called_from_cleanup:
            try:
                if websocket.client_state.name == "CONNECTED":
                    await websocket.close()
                    logger.debug(
                        f"Closed WebSocket connection via disconnect_websocket: {websocket}"
                    )
            except Exception:  # as e:
                # logger.debug(f"Exception closing WebSocket {websocket} in disconnect_websocket (already closed?): {e}")
                pass

    async def disconnect_from_task(
        self, task_id: str, websocket: WebSocket, called_from_cleanup: bool = False
    ):
        """Disconnect a WebSocket from a task, clean up, and check for task cancellation."""
        logger.debug(f"Disconnecting WebSocket {websocket} from task {task_id}.")

        # Remove from active_connections
        if task_id in self.active_connections:
            self.active_connections[task_id].discard(websocket)
            if not self.active_connections[task_id]:
                del self.active_connections[task_id]
                logger.debug(
                    f"Removed task {task_id} from active_connections (no more websockets)."
                )

        # Remove from client_responses
        if task_id in self.client_responses:
            if self.client_responses[task_id].pop(websocket, None):
                logger.debug(
                    f"Removed WebSocket {websocket} from client_responses for task {task_id}."
                )

        # Remove from task_subscribers
        if task_id in self.task_subscribers:
            self.task_subscribers[task_id].discard(websocket)
            if not self.task_subscribers[task_id]:
                del self.task_subscribers[task_id]
                logger.debug(
                    f"Removed task {task_id} from task_subscribers (no more subscribers)."
                )
            if not self.client_responses[task_id]:
                del self.client_responses[task_id]
                logger.debug(
                    f"Removed task {task_id} from client_responses (no more websockets)."
                )

        # After removing the specific websocket, check if the task itself needs cleanup
        await self._ensure_task_cleanup_if_no_clients(task_id)

        # Close the WebSocket connection if it's being explicitly disconnected and not part of a broader cleanup.
        # The cleanup loop handles its disconnections more broadly.
        if not called_from_cleanup:
            try:
                if websocket.client_state.name == "CONNECTED":
                    await websocket.close()
                    logger.debug(
                        f"Closed WebSocket {websocket} during explicit disconnect_from_task for {task_id}."
                    )
            except Exception:  # as e:
                # logger.debug(f"Exception closing WebSocket {websocket} in disconnect_from_task (already closed?): {e}")
                pass

    async def broadcast_to_task(self, task_id: str, message: dict):
        """Send message to all WebSocket connections for a task."""
        if task_id not in self.active_connections:
            return

        dead_connections = set()
        for websocket in self.active_connections[task_id]:
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {str(e)}")
                dead_connections.add(websocket)

        # Cleanup dead connections
        for ws in dead_connections:
            await self.disconnect_websocket(
                ws, called_from_cleanup=True
            )  # Indicate called from cleanup
            await self.disconnect_websocket(
                ws, called_from_cleanup=True
            )  # Indicate called from cleanup

    async def shutdown(self):
        """Shuts down the ConnectionManager's background tasks."""
        logger.info("Shutting down ConnectionManager cleanup task...")
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                # Wait for the task to acknowledge cancellation
                await asyncio.gather(self.cleanup_task, return_exceptions=True)
            except asyncio.CancelledError:
                logger.info(
                    "ConnectionManager cleanup task was cancelled successfully."
                )
            except Exception as e:
                logger.error(
                    f"Error during ConnectionManager cleanup task shutdown: {e}"
                )
        else:
            logger.info(
                "ConnectionManager cleanup task was already done or not started."
            )
        logger.info("ConnectionManager shutdown complete.")
