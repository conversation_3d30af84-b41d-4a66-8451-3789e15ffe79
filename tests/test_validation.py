#!/usr/bin/env python3
"""
Test script for enhanced validation system.

This script demonstrates the new validation capabilities implemented
in Phase 3 of the project improvements.
"""

from pydantic import ValidationError

from models.auth_schemas import APIKeyRequest
from models.file_schemas import FileUploadRequest
from models.schemas import SubtitleRequest, SummarizeRequest
from models.validators import (
    validate_client_uid,
    validate_filename,
    validate_youtube_url,
)


def test_youtube_url_validation():
    """Test YouTube URL validation."""
    print("🔗 Testing YouTube URL validation...")

    valid_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtube-nocookie.com/embed/dQw4w9WgXcQ",
    ]

    invalid_urls = [
        "https://example.com/video",
        "not-a-url",
        "https://youtube.com/watch?v=invalid",
        "https://vimeo.com/123456",
    ]

    for url in valid_urls:
        try:
            result = validate_youtube_url(url)
            print(f"  ✅ {url} -> Valid")
        except ValueError as e:
            print(f"  ❌ {url} -> Should be valid: {e}")

    for url in invalid_urls:
        try:
            validate_youtube_url(url)
            print(f"  ❌ {url} -> Should be invalid")
        except ValueError:
            print(f"  ✅ {url} -> Correctly rejected")


def test_client_uid_validation():
    """Test client UID validation (DEPRECATED - for backward compatibility only)."""
    print("\n👤 Testing Client UID validation (DEPRECATED)...")

    valid_uids = ["user_123", "app-mobile-v2", "test_user_001", "ABC123"]

    invalid_uids = [
        "ab",  # Too short
        "user@invalid",  # Invalid characters
        "user with spaces",  # Spaces
        "a" * 101,  # Too long
    ]

    for uid in valid_uids:
        try:
            result = validate_client_uid(uid)
            print(f"  ✅ '{uid}' -> Valid")
        except ValueError as e:
            print(f"  ❌ '{uid}' -> Should be valid: {e}")

    for uid in invalid_uids:
        try:
            validate_client_uid(uid)
            print(f"  ❌ '{uid}' -> Should be invalid")
        except ValueError:
            print(f"  ✅ '{uid}' -> Correctly rejected")


def test_subtitle_request_validation():
    """Test SubtitleRequest model validation."""
    print("\n📹 Testing SubtitleRequest validation...")

    # Valid request
    try:
        request = SubtitleRequest(
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            client_uid="test_user_123",
        )
        print(f"  ✅ Valid request: {request.url}, {request.client_uid}")
    except ValidationError as e:
        print(f"  ❌ Valid request failed: {e}")

    # Invalid URL
    try:
        request = SubtitleRequest(
            url="https://example.com/video", client_uid="test_user"
        )
        print("  ❌ Invalid URL should have failed")
    except ValidationError as e:
        print(f"  ✅ Invalid URL rejected: {e.errors()[0]['msg']}")

    # Invalid client_uid
    try:
        request = SubtitleRequest(
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ", client_uid="user@invalid"
        )
        print("  ❌ Invalid client_uid should have failed")
    except ValidationError as e:
        print(f"  ✅ Invalid client_uid rejected: {e.errors()[0]['msg']}")


def test_summarize_request_validation():
    """Test SummarizeRequest model validation."""
    print("\n📝 Testing SummarizeRequest validation...")

    # Valid request
    try:
        request = SummarizeRequest(
            client_uid="test_user",
            mode="default",
            og_text="This is a sample text that needs to be summarized. " * 10,
        )
        print(
            f"  ✅ Valid request: mode={request.mode}, text_length={len(request.og_text)}"
        )
    except ValidationError as e:
        print(f"  ❌ Valid request failed: {e}")

    # Text too short
    try:
        request = SummarizeRequest(og_text="Short")
        print("  ❌ Short text should have failed")
    except ValidationError as e:
        print(f"  ✅ Short text rejected: {e.errors()[0]['msg']}")

    # Text too long
    try:
        request = SummarizeRequest(og_text="A" * 600_000)  # Exceeds 500KB limit
        print("  ❌ Long text should have failed")
    except ValidationError as e:
        print(f"  ✅ Long text rejected: {e.errors()[0]['msg']}")


def test_api_key_request_validation():
    """Test APIKeyRequest model validation."""
    print("\n🔑 Testing APIKeyRequest validation...")

    # Valid request
    try:
        request = APIKeyRequest(
            name="Test API Key",
            permissions=["read", "write"],
            description="Test key for validation",
            expires_in_days=90,
        )
        print(f"  ✅ Valid request: {request.name}, permissions={request.permissions}")
    except ValidationError as e:
        print(f"  ❌ Valid request failed: {e}")

    # Invalid name (too short)
    try:
        request = APIKeyRequest(name="AB", permissions=["read"])
        print("  ❌ Short name should have failed")
    except ValidationError as e:
        print(f"  ✅ Short name rejected: {e.errors()[0]['msg']}")

    # Invalid permissions (empty)
    try:
        request = APIKeyRequest(name="Test Key", permissions=[])
        print("  ❌ Empty permissions should have failed")
    except ValidationError as e:
        print(f"  ✅ Empty permissions rejected: {e.errors()[0]['msg']}")


def test_file_upload_validation():
    """Test FileUploadRequest model validation."""
    print("\n📁 Testing FileUploadRequest validation...")

    import base64

    # Valid file
    try:
        content = base64.b64encode(b"This is a test file content").decode()
        request = FileUploadRequest(
            filename="test.txt", content=content, mime_type="text/plain"
        )
        print(f"  ✅ Valid file: {request.filename}, type={request.mime_type}")
    except ValidationError as e:
        print(f"  ❌ Valid file failed: {e}")

    # Invalid filename (path traversal)
    try:
        content = base64.b64encode(b"test").decode()
        request = FileUploadRequest(
            filename="../../../etc/passwd", content=content, mime_type="text/plain"
        )
        print("  ❌ Path traversal should have failed")
    except ValidationError as e:
        print(f"  ✅ Path traversal rejected: {e.errors()[0]['msg']}")

    # Invalid content (not base64)
    try:
        request = FileUploadRequest(
            filename="test.txt", content="not-base64-content!@#", mime_type="text/plain"
        )
        print("  ❌ Invalid base64 should have failed")
    except ValidationError as e:
        print(f"  ✅ Invalid base64 rejected: {e.errors()[0]['msg']}")


def test_filename_validation():
    """Test filename validation."""
    print("\n📄 Testing filename validation...")

    valid_filenames = ["document.txt", "my-file_v2.pdf", "report.docx", "notes.md"]

    invalid_filenames = [
        "../../../etc/passwd",  # Path traversal
        "file<script>.txt",  # HTML characters
        "file|pipe.txt",  # Reserved characters
        "a" * 300 + ".txt",  # Too long
    ]

    for filename in valid_filenames:
        try:
            result = validate_filename(filename)
            print(f"  ✅ '{filename}' -> Valid")
        except ValueError as e:
            print(f"  ❌ '{filename}' -> Should be valid: {e}")

    for filename in invalid_filenames:
        try:
            validate_filename(filename)
            print(f"  ❌ '{filename}' -> Should be invalid")
        except ValueError:
            print(f"  ✅ '{filename}' -> Correctly rejected")


def main():
    """Run all validation tests."""
    print("🧪 Enhanced Validation System Test Suite")
    print("=" * 50)

    test_youtube_url_validation()
    test_client_uid_validation()
    test_subtitle_request_validation()
    test_summarize_request_validation()
    test_api_key_request_validation()
    test_file_upload_validation()
    test_filename_validation()

    print("\n" + "=" * 50)
    print("✅ All validation tests completed!")
    print("\nThe enhanced validation system is working correctly:")
    print("  • YouTube URLs are properly validated")
    print("  • Client UIDs follow security constraints")
    print("  • Text content has appropriate limits")
    print("  • File uploads are secure")
    print("  • API key requests are validated")
    print("  • Filenames are checked for security")


if __name__ == "__main__":
    main()
