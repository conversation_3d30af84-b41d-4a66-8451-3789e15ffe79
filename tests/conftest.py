"""
Pytest configuration and shared fixtures for the test suite.
"""

import asyncio
import os
import tempfile
from collections.abc import As<PERSON><PERSON>enerator, Generator
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.app import create_app
from core.config import get_settings
from models.database import Base, db
from worker.queue import TaskQueue

# Test configuration
TEST_DATABASE_URL = "sqlite:///test_data.db"
TEST_API_KEY = "test_api_key_12345"
TEST_USER_DATA = {
    "name": "test_user",
    "permissions": ["read", "write"],
    "rate_limit": 100,
    "api_key": TEST_API_KEY,
}


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_settings():
    """Override settings for testing."""
    with patch.dict(
        os.environ,
        {
            "DATABASE_TYPE": "sqlite",
            "REQUIRE_AUTH": "false",
            "RATE_LIMITING_ENABLED": "false",
            "LOG_LEVEL": "DEBUG",
            "STRUCTURED_LOGGING": "false",
            "PROMETHEUS_ENABLED": "false",
            "SECURITY_HEADERS_ENABLED": "false",
        },
    ):
        settings = get_settings()
        yield settings


@pytest.fixture
def test_db():
    """Create a test database."""
    # Create test database
    engine = create_engine(TEST_DATABASE_URL, echo=False)
    Base.metadata.create_all(bind=engine)

    # Create session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()

    # Override database dependency
    original_get_db = db.get_db

    def override_get_db():
        try:
            yield session
        finally:
            session.close()

    db.get_db = override_get_db

    yield session

    # Cleanup
    session.close()
    db.get_db = original_get_db

    # Remove test database file
    if os.path.exists("test_data.db"):
        os.remove("test_data.db")


@pytest.fixture
def mock_task_queue():
    """Mock task queue for testing."""
    mock_queue = AsyncMock(spec=TaskQueue)

    # Mock common methods
    mock_queue.initialize = AsyncMock()
    mock_queue.shutdown = AsyncMock()
    mock_queue.add_subtitle_task = AsyncMock()
    mock_queue.add_summarize_task = AsyncMock()
    mock_queue.add_video_list_task = AsyncMock()
    mock_queue.get_task_status = AsyncMock()

    return mock_queue


@pytest.fixture
def mock_auth():
    """Mock authentication for testing."""
    with patch("api.middleware.auth.api_key_manager") as mock_manager:
        mock_manager.validate_api_key.return_value = TEST_USER_DATA
        mock_manager.require_auth = False
        yield mock_manager


@pytest.fixture
def app(test_settings, mock_task_queue, mock_auth):
    """Create FastAPI test application."""
    app = create_app()

    # Override task queue
    app.state.task_queue = mock_task_queue

    return app


@pytest.fixture
def client(app) -> Generator[TestClient, None, None]:
    """Create test client."""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def auth_headers():
    """Authentication headers for testing."""
    return {"Authorization": f"Bearer {TEST_API_KEY}"}


@pytest.fixture
def sample_youtube_urls():
    """Sample YouTube URLs for testing."""
    return {
        "valid": [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
        ],
        "invalid": [
            "https://example.com/video",
            "not-a-url",
            "https://vimeo.com/123456",
        ],
    }


@pytest.fixture
def sample_text_content():
    """Sample text content for testing."""
    return {
        "short": "This is a short text.",
        "medium": "This is a medium length text that should be suitable for testing summarization functionality. "
        * 10,
        "long": "This is a very long text that exceeds normal limits. " * 1000,
        "empty": "",
        "special_chars": "Text with special characters: <script>alert('xss')</script> & other symbols!",
    }


@pytest.fixture
def sample_api_keys():
    """Sample API key data for testing."""
    return {
        "valid": {
            "name": "Test API Key",
            "permissions": ["read", "write"],
            "description": "Test key for unit tests",
            "expires_in_days": 90,
        },
        "admin": {
            "name": "Admin API Key",
            "permissions": ["read", "write", "admin"],
            "description": "Admin test key",
            "expires_in_days": 365,
        },
        "read_only": {
            "name": "Read Only Key",
            "permissions": ["read"],
            "description": "Read-only test key",
            "expires_in_days": 30,
        },
    }


@pytest.fixture
def mock_youtube_downloader():
    """Mock YouTube subtitle downloader."""
    with patch("worker.subtitles.youtube.YouTubeSubtitleDownloader") as mock:
        mock_instance = mock.return_value
        mock_instance.download_subtitles = AsyncMock(
            return_value={
                "title": "Test Video",
                "original_language": "en",
                "publish_date": "2023-01-01",
                "en_subtitles": "English subtitle content",
                "ru_subtitles": "Russian subtitle content",
            }
        )
        mock_instance.get_video_ids = AsyncMock(
            return_value=["dQw4w9WgXcQ", "another_video_id"]
        )
        yield mock_instance


@pytest.fixture
def mock_text_summarizer():
    """Mock text summarizer."""
    with patch("worker.summarizers.summarizer.TextSummarizer") as mock:
        mock_instance = mock.return_value
        mock_instance.summarize = AsyncMock(
            return_value="This is a summarized version of the input text."
        )
        yield mock_instance


@pytest.fixture
def mock_gemini_api():
    """Mock Gemini AI API responses."""
    with patch("google.generativeai.GenerativeModel") as mock:
        mock_model = mock.return_value
        mock_response = MagicMock()
        mock_response.text = "Mocked AI response"
        mock_model.generate_content = AsyncMock(return_value=mock_response)
        yield mock_model


@pytest.fixture
def sample_ttml_content():
    """Sample TTML content for converter testing."""
    return """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
      <p begin="00:00:01.000" end="00:00:05.000">Hello, this is a test subtitle.</p>
      <p begin="00:00:06.000" end="00:00:10.000">This is another subtitle line.</p>
    </div>
  </body>
</tt>"""


@pytest.fixture
def sample_file_uploads():
    """Sample file upload data for testing."""
    import base64

    return {
        "text_file": {
            "filename": "test.txt",
            "content": base64.b64encode(b"This is test file content").decode(),
            "mime_type": "text/plain",
        },
        "large_file": {
            "filename": "large.txt",
            "content": base64.b64encode(b"Large content " * 10000).decode(),
            "mime_type": "text/plain",
        },
        "invalid_file": {
            "filename": "../../../etc/passwd",
            "content": "invalid-base64-content",
            "mime_type": "text/plain",
        },
    }


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: mark test as unit test")
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "e2e: mark test as end-to-end test")
    config.addinivalue_line("markers", "slow: mark test as slow running")
    config.addinivalue_line("markers", "auth: mark test as requiring authentication")
    config.addinivalue_line("markers", "websocket: mark test as WebSocket test")


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark tests based on directory structure
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)

        # Mark WebSocket tests
        if "websocket" in str(item.fspath) or "ws" in item.name.lower():
            item.add_marker(pytest.mark.websocket)

        # Mark auth tests
        if "auth" in str(item.fspath) or "auth" in item.name.lower():
            item.add_marker(pytest.mark.auth)
