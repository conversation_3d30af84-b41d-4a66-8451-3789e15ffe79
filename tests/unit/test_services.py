"""
Unit tests for service classes.
"""

from unittest.mock import AsyncMock, MagicMock

import pytest

from services.converter_service import ConverterService
from services.subtitle_service import SubtitleService
from services.summarize_service import SummarizeService
from services.task_service import TaskService


class TestConverterService:
    """Test ConverterService functionality."""

    def test_converter_service_creation(self):
        """Test ConverterService can be created."""
        service = ConverterService()
        assert isinstance(service, ConverterService)

    def test_ttml_to_txt_conversion_valid(self, sample_ttml_content):
        """Test TTML to TXT conversion with valid content."""
        service = ConverterService()

        result = service.convert_ttml_to_txt(sample_ttml_content)

        assert isinstance(result, str)
        assert len(result) > 0

        # Check that subtitle text is extracted
        assert "Hello, this is a test subtitle." in result
        assert "This is another subtitle line." in result

    def test_ttml_to_txt_conversion_empty(self):
        """Test TTML to TXT conversion with empty content."""
        service = ConverterService()

        with pytest.raises(ValueError):
            service.convert_ttml_to_txt("")

    def test_ttml_to_txt_conversion_invalid_xml(self):
        """Test TTML to TXT conversion with invalid XML."""
        service = ConverterService()

        invalid_ttml = "This is not valid XML content"

        with pytest.raises(ValueError):
            service.convert_ttml_to_txt(invalid_ttml)

    def test_ttml_to_txt_conversion_no_subtitles(self):
        """Test TTML to TXT conversion with no subtitle content."""
        service = ConverterService()

        empty_ttml = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
    </div>
  </body>
</tt>"""

        result = service.convert_ttml_to_txt(empty_ttml)
        assert result == ""

    def test_ttml_to_txt_conversion_malformed_ttml(self):
        """Test TTML to TXT conversion with malformed TTML."""
        service = ConverterService()

        malformed_ttml = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
      <p begin="invalid_time">Test subtitle</p>
    </div>
  </body>
</tt>"""

        # Should still extract text even with malformed timestamps
        result = service.convert_ttml_to_txt(malformed_ttml)
        assert "Test subtitle" in result

    def test_ttml_to_txt_conversion_multiple_paragraphs(self):
        """Test TTML to TXT conversion with multiple paragraphs."""
        service = ConverterService()

        multi_ttml = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
      <p begin="00:00:01.000" end="00:00:03.000">First subtitle</p>
      <p begin="00:00:04.000" end="00:00:06.000">Second subtitle</p>
      <p begin="00:00:07.000" end="00:00:09.000">Third subtitle</p>
    </div>
  </body>
</tt>"""

        result = service.convert_ttml_to_txt(multi_ttml)

        assert "First subtitle" in result
        assert "Second subtitle" in result
        assert "Third subtitle" in result

    def test_ttml_to_txt_conversion_with_formatting(self):
        """Test TTML to TXT conversion preserving text formatting."""
        service = ConverterService()

        formatted_ttml = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
  <body>
    <div>
      <p begin="00:00:01.000" end="00:00:03.000">
        <span style="fontWeight:bold">Bold text</span> and normal text
      </p>
    </div>
  </body>
</tt>"""

        result = service.convert_ttml_to_txt(formatted_ttml)

        # Should extract text content regardless of formatting
        assert "Bold text and normal text" in result


class TestSubtitleService:
    """Test SubtitleService functionality."""

    def test_subtitle_service_creation(self):
        """Test SubtitleService can be created."""
        mock_task_queue = MagicMock()
        service = SubtitleService(mock_task_queue)
        assert isinstance(service, SubtitleService)

    @pytest.mark.asyncio
    async def test_get_video_ids(self, sample_youtube_urls):
        """Test getting video IDs from URL."""
        mock_task_queue = AsyncMock()
        mock_response = MagicMock()
        mock_response.task_id = "video_list_task_123"
        mock_response.status = "pending"
        mock_task_queue.add_video_list_task.return_value = mock_response

        service = SubtitleService(mock_task_queue)

        from models.schemas import SubtitleRequest

        request = SubtitleRequest(url=sample_youtube_urls["valid"][0])

        result = await service.get_video_ids(request)

        assert result.task_id == "video_list_task_123"
        assert result.status == "pending"
        mock_task_queue.add_video_list_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_subtitles_task(self, sample_youtube_urls):
        """Test creating subtitle extraction task."""
        mock_task_queue = AsyncMock()
        mock_response = MagicMock()
        mock_response.task_id = "subtitle_task_123"
        mock_response.status = "pending"
        mock_task_queue.add_subtitle_task.return_value = mock_response

        service = SubtitleService(mock_task_queue)

        from models.schemas import SubtitleRequest

        request = SubtitleRequest(url=sample_youtube_urls["valid"][0])

        result = await service.create_subtitles_task(request)

        assert result.task_id == "subtitle_task_123"
        assert result.status == "pending"
        mock_task_queue.add_subtitle_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_subtitle_service_error_handling(self, sample_youtube_urls):
        """Test SubtitleService error handling."""
        mock_task_queue = AsyncMock()
        mock_task_queue.add_subtitle_task.side_effect = Exception("Queue error")

        service = SubtitleService(mock_task_queue)

        from models.schemas import SubtitleRequest

        request = SubtitleRequest(url=sample_youtube_urls["valid"][0])

        with pytest.raises(Exception):
            await service.create_subtitles_task(request)


class TestSummarizeService:
    """Test SummarizeService functionality."""

    def test_summarize_service_creation(self):
        """Test SummarizeService can be created."""
        mock_task_queue = MagicMock()
        service = SummarizeService(mock_task_queue)
        assert isinstance(service, SummarizeService)

    @pytest.mark.asyncio
    async def test_create_summarize_task(self, sample_text_content):
        """Test creating text summarization task."""
        mock_task_queue = AsyncMock()
        mock_response = MagicMock()
        mock_response.task_id = "summarize_task_123"
        mock_response.status = "pending"
        mock_task_queue.add_summarize_task.return_value = mock_response

        service = SummarizeService(mock_task_queue)

        from models.schemas import SummarizeRequest

        request = SummarizeRequest(og_text=sample_text_content["medium"])

        result = await service.create_summarize_task(request)

        assert result.task_id == "summarize_task_123"
        assert result.status == "pending"
        mock_task_queue.add_summarize_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_summarize_file_task(self, sample_file_uploads):
        """Test creating file summarization task."""
        mock_task_queue = AsyncMock()
        mock_response = MagicMock()
        mock_response.task_id = "file_summarize_task_123"
        mock_response.status = "pending"
        mock_task_queue.add_summarize_task.return_value = mock_response

        service = SummarizeService(mock_task_queue)

        from io import BytesIO

        from fastapi import UploadFile

        # Create mock file upload
        file_content = sample_file_uploads["text_file"]["content"]
        file_data = BytesIO(file_content.encode())
        upload_file = UploadFile(
            filename=sample_file_uploads["text_file"]["filename"], file=file_data
        )

        result = await service.create_summarize_file_task(upload_file, mode="default")

        assert result.task_id == "file_summarize_task_123"
        assert result.status == "pending"

    @pytest.mark.asyncio
    async def test_summarize_service_error_handling(self, sample_text_content):
        """Test SummarizeService error handling."""
        mock_task_queue = AsyncMock()
        mock_task_queue.add_summarize_task.side_effect = Exception("Queue error")

        service = SummarizeService(mock_task_queue)

        from models.schemas import SummarizeRequest

        request = SummarizeRequest(og_text=sample_text_content["medium"])

        with pytest.raises(Exception):
            await service.create_summarize_task(request)


class TestTaskService:
    """Test TaskService functionality."""

    def test_task_service_creation(self):
        """Test TaskService can be created."""
        mock_task_queue = MagicMock()
        service = TaskService(mock_task_queue)
        assert isinstance(service, TaskService)

    @pytest.mark.asyncio
    async def test_get_task_status(self):
        """Test getting task status."""
        mock_task_queue = AsyncMock()
        mock_response = MagicMock()
        mock_response.task_id = "test_task_123"
        mock_response.status = "completed"
        mock_response.result = {"title": "Test Video"}
        mock_task_queue.get_task_status.return_value = mock_response

        service = TaskService(mock_task_queue)

        result = await service.get_task_status("test_task_123")

        assert result.task_id == "test_task_123"
        assert result.status == "completed"
        mock_task_queue.get_task_status.assert_called_once_with("test_task_123")

    @pytest.mark.asyncio
    async def test_get_task_status_not_found(self):
        """Test getting status of non-existent task."""
        mock_task_queue = AsyncMock()
        mock_task_queue.get_task_status.return_value = None

        service = TaskService(mock_task_queue)

        result = await service.get_task_status("nonexistent_task")

        assert result is None

    @pytest.mark.asyncio
    async def test_task_service_error_handling(self):
        """Test TaskService error handling."""
        mock_task_queue = AsyncMock()
        mock_task_queue.get_task_status.side_effect = Exception("Database error")

        service = TaskService(mock_task_queue)

        with pytest.raises(Exception):
            await service.get_task_status("test_task_123")

    @pytest.mark.asyncio
    async def test_cancel_task(self):
        """Test canceling a task."""
        mock_task_queue = AsyncMock()
        mock_task_queue.cancel_task.return_value = True

        service = TaskService(mock_task_queue)

        # Assuming cancel_task method exists
        if hasattr(service, "cancel_task"):
            result = await service.cancel_task("test_task_123")
            assert result is True

    @pytest.mark.asyncio
    async def test_list_tasks(self):
        """Test listing tasks."""
        mock_task_queue = AsyncMock()
        mock_tasks = [
            MagicMock(task_id="task1", status="completed"),
            MagicMock(task_id="task2", status="pending"),
        ]
        mock_task_queue.list_tasks.return_value = mock_tasks

        service = TaskService(mock_task_queue)

        # Assuming list_tasks method exists
        if hasattr(service, "list_tasks"):
            result = await service.list_tasks()
            assert len(result) == 2
            assert result[0].task_id == "task1"
            assert result[1].task_id == "task2"


class TestServiceIntegration:
    """Test service integration scenarios."""

    @pytest.mark.asyncio
    async def test_subtitle_to_summarize_workflow(
        self, sample_youtube_urls, sample_text_content
    ):
        """Test workflow from subtitle extraction to summarization."""
        mock_task_queue = AsyncMock()

        # Mock subtitle service response
        subtitle_response = MagicMock()
        subtitle_response.task_id = "subtitle_task_123"
        subtitle_response.status = "completed"
        subtitle_response.en_subtitles = sample_text_content["medium"]
        mock_task_queue.add_subtitle_task.return_value = subtitle_response

        # Mock summarize service response
        summarize_response = MagicMock()
        summarize_response.task_id = "summarize_task_456"
        summarize_response.status = "pending"
        mock_task_queue.add_summarize_task.return_value = summarize_response

        # Test workflow
        subtitle_service = SubtitleService(mock_task_queue)
        summarize_service = SummarizeService(mock_task_queue)

        from models.schemas import SubtitleRequest, SummarizeRequest

        # Step 1: Extract subtitles
        subtitle_request = SubtitleRequest(url=sample_youtube_urls["valid"][0])
        subtitle_result = await subtitle_service.create_subtitles_task(subtitle_request)

        # Step 2: Summarize extracted text
        summarize_request = SummarizeRequest(og_text=sample_text_content["medium"])
        summarize_result = await summarize_service.create_summarize_task(
            summarize_request
        )

        assert subtitle_result.task_id == "subtitle_task_123"
        assert summarize_result.task_id == "summarize_task_456"

    @pytest.mark.asyncio
    async def test_service_error_propagation(self, sample_youtube_urls):
        """Test error propagation between services."""
        mock_task_queue = AsyncMock()
        mock_task_queue.add_subtitle_task.side_effect = Exception("Queue full")

        subtitle_service = SubtitleService(mock_task_queue)
        task_service = TaskService(mock_task_queue)

        from models.schemas import SubtitleRequest

        # Service should propagate errors
        request = SubtitleRequest(url=sample_youtube_urls["valid"][0])

        with pytest.raises(Exception) as exc_info:
            await subtitle_service.create_subtitles_task(request)

        assert "Queue full" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_service_dependency_injection(self):
        """Test service dependency injection."""
        mock_task_queue = AsyncMock()

        # All services should accept the same task queue
        subtitle_service = SubtitleService(mock_task_queue)
        summarize_service = SummarizeService(mock_task_queue)
        task_service = TaskService(mock_task_queue)

        assert subtitle_service.task_queue is mock_task_queue
        assert summarize_service.task_queue is mock_task_queue
        assert task_service.task_queue is mock_task_queue

    def test_service_factory_pattern(self):
        """Test service factory pattern."""
        from services.factory import ServiceFactory

        mock_task_queue = MagicMock()

        # Assuming ServiceFactory exists
        if hasattr(ServiceFactory, "create_subtitle_service"):
            subtitle_service = ServiceFactory.create_subtitle_service(mock_task_queue)
            assert isinstance(subtitle_service, SubtitleService)

        if hasattr(ServiceFactory, "create_summarize_service"):
            summarize_service = ServiceFactory.create_summarize_service(mock_task_queue)
            assert isinstance(summarize_service, SummarizeService)

        if hasattr(ServiceFactory, "create_task_service"):
            task_service = ServiceFactory.create_task_service(mock_task_queue)
            assert isinstance(task_service, TaskService)


class TestServiceConfiguration:
    """Test service configuration and settings."""

    def test_converter_service_configuration(self):
        """Test ConverterService configuration."""
        service = ConverterService()

        # Test default configuration
        assert hasattr(service, "convert_ttml_to_txt")

        # Test configuration options if available
        if hasattr(service, "config"):
            assert service.config is not None

    def test_service_logging_configuration(self):
        """Test service logging configuration."""
        mock_task_queue = MagicMock()

        subtitle_service = SubtitleService(mock_task_queue)
        summarize_service = SummarizeService(mock_task_queue)
        task_service = TaskService(mock_task_queue)

        # Services should have logger configured
        assert hasattr(subtitle_service, "logger")
        assert hasattr(summarize_service, "logger")
        assert hasattr(task_service, "logger")

    def test_service_timeout_configuration(self):
        """Test service timeout configuration."""
        mock_task_queue = MagicMock()

        # Services should handle timeouts appropriately
        subtitle_service = SubtitleService(mock_task_queue)

        # Test timeout configuration if available
        if hasattr(subtitle_service, "timeout"):
            assert subtitle_service.timeout > 0
