"""
Unit tests for configuration settings.
"""

import os
from unittest.mock import patch

import pytest

from core.config import Settings, get_settings


class TestSettings:
    """Test Settings configuration class."""

    def test_default_settings(self):
        """Test default settings values."""
        # Test with clean environment (no .env file loading)
        with patch("core.config.load_dotenv"):
            with patch.dict(os.environ, {}, clear=True):
                from core.config import Settings

                settings = Settings()

                # Application settings
                assert (
                    settings.APP_NAME == "YouTube Subtitles and Text Summarization API"
                )
                assert settings.APP_VERSION == "0.1.0"
                assert settings.DEBUG is False

        # Server settings
        assert settings.HOST == "0.0.0.0"
        assert settings.PORT == 8000
        assert settings.RELOAD is False

        # API settings
        assert settings.API_PREFIX == "/api"
        assert settings.DOCS_URL == "/docs"
        assert settings.REDOC_URL == "/redoc"
        assert settings.OPENAPI_URL == "/openapi.json"

    def test_task_queue_settings(self):
        """Test task queue configuration."""
        settings = Settings()

        assert settings.MAX_SUBTITLE_WORKERS == 1
        assert settings.MAX_SUMMARIZE_WORKERS == 2
        assert settings.SUBTITLE_QUEUE_SIZE == 10
        assert settings.SUMMARIZE_QUEUE_SIZE == 10

    def test_websocket_settings(self):
        """Test WebSocket configuration."""
        settings = Settings()

        assert settings.WEBSOCKET_PING_INTERVAL == 30
        assert settings.WEBSOCKET_PING_TIMEOUT == 5

    def test_logging_settings(self):
        """Test logging configuration."""
        settings = Settings()

        assert settings.LOG_LEVEL == "INFO"
        assert settings.STRUCTURED_LOGGING is True
        assert settings.LOG_JSON_FORMAT is False
        assert settings.LOG_REQUEST_BODY is False
        assert settings.LOG_RESPONSE_BODY is False
        assert "/health" in settings.LOG_EXCLUDE_PATHS
        assert "/metrics" in settings.LOG_EXCLUDE_PATHS

    def test_cors_settings(self):
        """Test CORS configuration."""
        settings = Settings()

        assert settings.CORS_ORIGINS == "*"
        assert settings.CORS_ALLOW_CREDENTIALS is True
        assert "GET,POST,PUT,DELETE,OPTIONS" in settings.CORS_ALLOW_METHODS
        assert "Content-Type,Authorization" in settings.CORS_ALLOW_HEADERS

    def test_auth_settings(self):
        """Test authentication configuration."""
        settings = Settings()

        assert settings.REQUIRE_AUTH is True
        assert settings.API_KEYS is None

    def test_rate_limiting_settings(self):
        """Test rate limiting configuration."""
        settings = Settings()

        assert settings.RATE_LIMITING_ENABLED is True

        # Anonymous user limits
        assert settings.ANONYMOUS_RATE_LIMIT_MINUTE == 10
        assert settings.ANONYMOUS_RATE_LIMIT_HOUR == 100
        assert settings.ANONYMOUS_RATE_LIMIT_DAY == 500

        # Read-only user limits
        assert settings.READ_RATE_LIMIT_MINUTE == 30
        assert settings.READ_RATE_LIMIT_HOUR == 500
        assert settings.READ_RATE_LIMIT_DAY == 2000

        # Read-write user limits
        assert settings.WRITE_RATE_LIMIT_MINUTE == 60
        assert settings.WRITE_RATE_LIMIT_HOUR == 1000
        assert settings.WRITE_RATE_LIMIT_DAY == 5000

        # Admin user limits
        assert settings.ADMIN_RATE_LIMIT_MINUTE == 120
        assert settings.ADMIN_RATE_LIMIT_HOUR == 2000
        assert settings.ADMIN_RATE_LIMIT_DAY == 10000

    def test_endpoint_specific_rate_limits(self):
        """Test endpoint-specific rate limiting."""
        settings = Settings()

        # Subtitles limits (more resource intensive)
        assert settings.SUBTITLES_RATE_LIMIT_MINUTE == 10
        assert settings.SUBTITLES_RATE_LIMIT_HOUR == 100
        assert settings.SUBTITLES_RATE_LIMIT_DAY == 500

        # Summarization limits (very resource intensive)
        assert settings.SUMMARIZE_RATE_LIMIT_MINUTE == 5
        assert settings.SUMMARIZE_RATE_LIMIT_HOUR == 50
        assert settings.SUMMARIZE_RATE_LIMIT_DAY == 200

    def test_database_settings(self):
        """Test database configuration."""
        settings = Settings()

        assert settings.DATABASE_TYPE == "sqlite"
        assert settings.POSTGRES_HOST is None
        assert settings.POSTGRES_PORT is None
        assert settings.POSTGRES_USER is None
        assert settings.POSTGRES_PASSWORD is None
        assert settings.POSTGRES_DB is None

    def test_external_api_settings(self):
        """Test external API configuration."""
        settings = Settings()

        assert settings.YOUTUBE_API_KEY is None
        assert settings.GEM_API is None
        assert settings.SOCKS5_PROXY is None
        assert settings.COOKIES_FILE is None


class TestSettingsFromEnvironment:
    """Test Settings loading from environment variables."""

    def test_settings_from_env_vars(self):
        """Test loading settings from environment variables."""
        env_vars = {
            "APP_NAME": "Test API",
            "HOST": "127.0.0.1",
            "PORT": "9000",
            "DEBUG": "true",
            "RELOAD": "true",
            "LOG_LEVEL": "DEBUG",
            "REQUIRE_AUTH": "false",
            "RATE_LIMITING_ENABLED": "false",
            "DATABASE_TYPE": "postgresql",
            "POSTGRES_HOST": "localhost",
            "POSTGRES_PORT": "5432",
            "POSTGRES_USER": "testuser",
            "POSTGRES_PASSWORD": "testpass",
            "POSTGRES_DB": "testdb",
            "GEM_API": "test_api_key",
            "YOUTUBE_API_KEY": "test_youtube_key",
            "SOCKS5_PROXY": "socks5://127.0.0.1:1080",
            "COOKIES_FILE": "/path/to/cookies.txt",
        }

        with patch.dict(os.environ, env_vars):
            settings = Settings()

            assert settings.APP_NAME == "Test API"
            assert settings.HOST == "127.0.0.1"
            assert settings.PORT == 9000
            assert settings.DEBUG is True
            assert settings.RELOAD is True
            assert settings.LOG_LEVEL == "DEBUG"
            assert settings.REQUIRE_AUTH is False
            assert settings.RATE_LIMITING_ENABLED is False
            assert settings.DATABASE_TYPE == "postgresql"
            assert settings.POSTGRES_HOST == "localhost"
            assert settings.POSTGRES_PORT == 5432
            assert settings.POSTGRES_USER == "testuser"
            assert settings.POSTGRES_PASSWORD == "testpass"
            assert settings.POSTGRES_DB == "testdb"
            assert settings.GEM_API == "test_api_key"
            assert settings.YOUTUBE_API_KEY == "test_youtube_key"
            assert settings.SOCKS5_PROXY == "socks5://127.0.0.1:1080"
            assert settings.COOKIES_FILE == "/path/to/cookies.txt"

    def test_cors_origins_parsing(self):
        """Test CORS origins parsing from environment."""
        # Test single origin
        with patch.dict(os.environ, {"CORS_ORIGINS": "https://example.com"}):
            settings = Settings()
            assert settings.CORS_ORIGINS == "https://example.com"

        # Test multiple origins
        with patch.dict(
            os.environ, {"CORS_ORIGINS": "https://example.com,https://app.example.com"}
        ):
            settings = Settings()
            assert (
                settings.CORS_ORIGINS == "https://example.com,https://app.example.com"
            )

        # Test wildcard
        with patch.dict(os.environ, {"CORS_ORIGINS": "*"}):
            settings = Settings()
            assert settings.CORS_ORIGINS == "*"

    def test_rate_limit_overrides(self):
        """Test rate limit overrides from environment."""
        env_vars = {
            "ANONYMOUS_RATE_LIMIT_MINUTE": "20",
            "READ_RATE_LIMIT_HOUR": "1000",
            "WRITE_RATE_LIMIT_DAY": "10000",
            "SUBTITLES_RATE_LIMIT_MINUTE": "5",
            "SUMMARIZE_RATE_LIMIT_HOUR": "25",
        }

        with patch.dict(os.environ, env_vars):
            settings = Settings()

            assert settings.ANONYMOUS_RATE_LIMIT_MINUTE == 20
            assert settings.READ_RATE_LIMIT_HOUR == 1000
            assert settings.WRITE_RATE_LIMIT_DAY == 10000
            assert settings.SUBTITLES_RATE_LIMIT_MINUTE == 5
            assert settings.SUMMARIZE_RATE_LIMIT_HOUR == 25

    def test_boolean_env_vars(self):
        """Test boolean environment variable parsing."""
        # Test various boolean representations
        boolean_tests = [
            ("true", True),
            ("True", True),
            ("TRUE", True),
            ("1", True),
            ("yes", True),
            ("false", False),
            ("False", False),
            ("FALSE", False),
            ("0", False),
            ("no", False),
        ]

        for env_value, expected in boolean_tests:
            with patch.dict(os.environ, {"DEBUG": env_value}):
                settings = Settings()
                assert settings.DEBUG == expected

    def test_integer_env_vars(self):
        """Test integer environment variable parsing."""
        with patch.dict(os.environ, {"PORT": "8080", "MAX_SUBTITLE_WORKERS": "5"}):
            settings = Settings()
            assert settings.PORT == 8080
            assert settings.MAX_SUBTITLE_WORKERS == 5

    def test_invalid_env_vars(self):
        """Test handling of invalid environment variables."""
        # Invalid port (should use default or raise error)
        with patch.dict(os.environ, {"PORT": "invalid_port"}):
            with pytest.raises(ValueError):
                Settings()

    def test_security_headers_settings(self):
        """Test security headers configuration."""
        with patch.dict(os.environ, {"SECURITY_HEADERS_ENABLED": "true"}):
            settings = Settings()
            assert settings.SECURITY_HEADERS_ENABLED is True

        with patch.dict(os.environ, {"SECURITY_HEADERS_ENABLED": "false"}):
            settings = Settings()
            assert settings.SECURITY_HEADERS_ENABLED is False

    def test_prometheus_settings(self):
        """Test Prometheus metrics configuration."""
        # Test default (should be True if not set)
        settings = Settings()
        prometheus_enabled = getattr(settings, "PROMETHEUS_ENABLED", True)
        assert prometheus_enabled is True

        # Test explicit setting
        with patch.dict(os.environ, {"PROMETHEUS_ENABLED": "false"}):
            settings = Settings()
            prometheus_enabled = getattr(settings, "PROMETHEUS_ENABLED", True)
            # Note: This might be True if not defined in Settings class


class TestGetSettings:
    """Test get_settings function."""

    def test_get_settings_singleton(self):
        """Test that get_settings returns the same instance."""
        settings1 = get_settings()
        settings2 = get_settings()

        # Should be the same instance (singleton pattern)
        assert settings1 is settings2

    def test_get_settings_type(self):
        """Test that get_settings returns Settings instance."""
        settings = get_settings()
        assert isinstance(settings, Settings)

    def test_get_settings_with_env_override(self):
        """Test get_settings with environment override."""
        with patch.dict(os.environ, {"APP_NAME": "Override Test API"}):
            # Clear any cached settings
            get_settings.cache_clear()
            settings = get_settings()
            assert settings.APP_NAME == "Override Test API"


class TestConfigValidation:
    """Test configuration validation."""

    def test_log_level_validation(self):
        """Test log level validation."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

        for level in valid_levels:
            with patch.dict(os.environ, {"LOG_LEVEL": level}):
                settings = Settings()
                assert settings.LOG_LEVEL == level

    def test_database_type_validation(self):
        """Test database type validation."""
        valid_types = ["sqlite", "postgresql"]

        for db_type in valid_types:
            with patch.dict(os.environ, {"DATABASE_TYPE": db_type}):
                settings = Settings()
                assert settings.DATABASE_TYPE == db_type

    def test_cors_methods_validation(self):
        """Test CORS methods configuration."""
        settings = Settings()
        methods = settings.CORS_ALLOW_METHODS

        # Should contain common HTTP methods
        assert "GET" in methods
        assert "POST" in methods
        assert "PUT" in methods or "PATCH" in methods
        assert "DELETE" in methods
        assert "OPTIONS" in methods

    def test_cors_headers_validation(self):
        """Test CORS headers configuration."""
        settings = Settings()
        headers = settings.CORS_ALLOW_HEADERS

        # Should contain essential headers
        assert "Content-Type" in headers
        assert "Authorization" in headers
