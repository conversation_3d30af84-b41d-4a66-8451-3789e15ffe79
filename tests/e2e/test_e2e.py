"""
End-to-end tests for the complete application workflow.
"""

import time
from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient

from models.schemas import TaskStatus


class TestSubtitleWorkflow:
    """Test complete subtitle extraction workflow."""

    def test_subtitle_extraction_rest_api(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test complete subtitle extraction via REST API."""
        # Step 1: Create subtitle task
        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        if response.status_code != 202:
            pytest.skip("Subtitle endpoint not available or auth required")

        data = response.json()
        task_id = data["task_id"]

        # Step 2: Poll for task completion
        max_attempts = 10
        for attempt in range(max_attempts):
            status_response = client.get(f"/api/task/{task_id}", headers=auth_headers)

            if status_response.status_code == 200:
                status_data = status_response.json()

                if status_data["status"] == TaskStatus.COMPLETED:
                    # Step 3: Verify results
                    assert "title" in status_data
                    assert (
                        "en_subtitles" in status_data or "ru_subtitles" in status_data
                    )
                    break
                elif status_data["status"] == TaskStatus.FAILED:
                    pytest.fail(
                        f"Task failed: {status_data.get('error', 'Unknown error')}"
                    )

            time.sleep(1)  # Wait before next poll
        else:
            pytest.skip("Task did not complete within timeout")

    def test_subtitle_extraction_websocket(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test complete subtitle extraction via WebSocket."""
        try:
            with client.websocket_connect("/ws/subtitles") as websocket:
                # Step 1: Send subtitle request
                request_data = {"url": sample_youtube_urls["valid"][0]}
                websocket.send_json(request_data)

                # Step 2: Receive status updates
                max_attempts = 10
                for attempt in range(max_attempts):
                    try:
                        response = websocket.receive_json()

                        if response.get("status") == TaskStatus.COMPLETED:
                            # Step 3: Verify results
                            assert "task_id" in response
                            assert "title" in response
                            assert (
                                "en_subtitles" in response or "ru_subtitles" in response
                            )
                            break
                        elif response.get("status") == TaskStatus.FAILED:
                            pytest.fail(
                                f"Task failed: {response.get('error', 'Unknown error')}"
                            )
                    except Exception:
                        time.sleep(1)
                        continue
                else:
                    pytest.skip("WebSocket task did not complete within timeout")
        except Exception:
            pytest.skip("WebSocket connection failed")

    def test_video_id_extraction(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test video ID extraction workflow."""
        response = client.post(
            "/api/subtitles/get_id",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        if response.status_code not in [200, 202]:
            pytest.skip("Video ID endpoint not available or auth required")

        data = response.json()

        if response.status_code == 202:
            # Async processing - poll for results
            task_id = data["task_id"]

            max_attempts = 5
            for attempt in range(max_attempts):
                status_response = client.get(
                    f"/api/task/{task_id}", headers=auth_headers
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()

                    if status_data["status"] == TaskStatus.COMPLETED:
                        assert "video_ids" in status_data
                        break

                time.sleep(1)
        else:
            # Synchronous response
            assert "video_ids" in data


class TestSummarizationWorkflow:
    """Test complete text summarization workflow."""

    def test_text_summarization_rest_api(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test complete text summarization via REST API."""
        # Step 1: Create summarization task
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["medium"]},
            headers=auth_headers,
        )

        if response.status_code != 202:
            pytest.skip("Summarize endpoint not available or auth required")

        data = response.json()
        task_id = data["task_id"]

        # Step 2: Poll for task completion
        max_attempts = 10
        for attempt in range(max_attempts):
            status_response = client.get(f"/api/task/{task_id}", headers=auth_headers)

            if status_response.status_code == 200:
                status_data = status_response.json()

                if status_data["status"] == TaskStatus.COMPLETED:
                    # Step 3: Verify results
                    assert "summary" in status_data
                    assert len(status_data["summary"]) > 0
                    break
                elif status_data["status"] == TaskStatus.FAILED:
                    pytest.fail(
                        f"Task failed: {status_data.get('error', 'Unknown error')}"
                    )

            time.sleep(1)
        else:
            pytest.skip("Task did not complete within timeout")

    def test_text_summarization_websocket(
        self, client: TestClient, sample_text_content
    ):
        """Test complete text summarization via WebSocket."""
        try:
            with client.websocket_connect("/ws/summarize") as websocket:
                # Step 1: Send summarization request
                request_data = {
                    "type": "text_input",
                    "content": sample_text_content["medium"],
                    "mode": "default",
                }
                websocket.send_json(request_data)

                # Step 2: Receive status updates
                max_attempts = 10
                for attempt in range(max_attempts):
                    try:
                        response = websocket.receive_json()

                        if response.get("status") == TaskStatus.COMPLETED:
                            # Step 3: Verify results
                            assert "task_id" in response
                            assert "summary" in response
                            assert len(response["summary"]) > 0
                            break
                        elif response.get("status") == TaskStatus.FAILED:
                            pytest.fail(
                                f"Task failed: {response.get('error', 'Unknown error')}"
                            )
                    except Exception:
                        time.sleep(1)
                        continue
                else:
                    pytest.skip("WebSocket task did not complete within timeout")
        except Exception:
            pytest.skip("WebSocket connection failed")

    def test_file_summarization(
        self, client: TestClient, sample_file_uploads, auth_headers
    ):
        """Test file summarization workflow."""
        file_data = sample_file_uploads["text_file"]

        # Create multipart form data
        files = {
            "file": (
                file_data["filename"],
                file_data["content"],
                file_data["mime_type"],
            )
        }

        response = client.post("/api/summarize/file", files=files, headers=auth_headers)

        if response.status_code not in [200, 202]:
            pytest.skip("File summarization endpoint not available")

        if response.status_code == 202:
            data = response.json()
            task_id = data["task_id"]

            # Poll for completion
            max_attempts = 10
            for attempt in range(max_attempts):
                status_response = client.get(
                    f"/api/task/{task_id}", headers=auth_headers
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()

                    if status_data["status"] == TaskStatus.COMPLETED:
                        assert "summary" in status_data
                        break

                time.sleep(1)


class TestConverterWorkflow:
    """Test TTML to TXT conversion workflow."""

    def test_ttml_to_txt_conversion(self, client: TestClient, sample_ttml_content):
        """Test TTML to TXT conversion workflow."""
        response = client.post(
            "/api/convert/ttml-to-txt", json={"ttml_content": sample_ttml_content}
        )

        if response.status_code == 401:
            pytest.skip("Converter endpoint requires authentication")

        if response.status_code == 200:
            data = response.json()
            assert "txt_content" in data
            assert len(data["txt_content"]) > 0

            # Verify conversion quality
            txt_content = data["txt_content"]
            assert "Hello, this is a test subtitle." in txt_content
            assert "This is another subtitle line." in txt_content
        else:
            pytest.skip("Converter endpoint not available")


class TestAuthenticationWorkflow:
    """Test authentication workflow."""

    def test_api_key_management_workflow(self, client: TestClient):
        """Test complete API key management workflow."""
        # This test would require admin authentication
        # Skip if not available in test environment
        pytest.skip("Admin authentication not available in test environment")

    def test_user_info_workflow(self, client: TestClient, auth_headers):
        """Test user information workflow."""
        response = client.get("/api/auth/me", headers=auth_headers)

        if response.status_code == 200:
            data = response.json()
            assert "name" in data
            assert "permissions" in data
            assert "rate_limit" in data
            assert "api_key_preview" in data
        else:
            pytest.skip("Authentication not properly configured in tests")


class TestErrorScenarios:
    """Test error scenarios and recovery."""

    def test_invalid_youtube_url_workflow(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test workflow with invalid YouTube URL."""
        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["invalid"][0]},
            headers=auth_headers,
        )

        # Should return validation error
        assert response.status_code == 422

    def test_text_too_short_workflow(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test workflow with text too short for summarization."""
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["short"]},
            headers=auth_headers,
        )

        # Should return validation error
        assert response.status_code == 422

    def test_nonexistent_task_workflow(self, client: TestClient, auth_headers):
        """Test workflow with non-existent task ID."""
        response = client.get("/api/task/nonexistent_task_id", headers=auth_headers)

        # Should return not found
        assert response.status_code == 404

    def test_malformed_request_workflow(self, client: TestClient, auth_headers):
        """Test workflow with malformed request."""
        response = client.post(
            "/api/subtitles",
            json={"invalid_field": "invalid_value"},
            headers=auth_headers,
        )

        # Should return validation error
        assert response.status_code == 422


class TestPerformanceScenarios:
    """Test performance scenarios."""

    def test_concurrent_requests_workflow(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test concurrent request handling."""
        import threading

        results = []

        def make_request():
            response = client.post(
                "/api/subtitles",
                json={"url": sample_youtube_urls["valid"][0]},
                headers=auth_headers,
            )
            results.append(response.status_code)

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Check results
        if results:
            # Should handle concurrent requests
            assert all(status in [202, 401, 429] for status in results)

    def test_rapid_sequential_requests(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test rapid sequential requests."""
        results = []

        for i in range(5):
            response = client.post(
                "/api/subtitles",
                json={"url": sample_youtube_urls["valid"][0]},
                headers=auth_headers,
            )
            results.append(response.status_code)

        if results:
            # Should handle rapid requests (might be rate limited)
            assert all(status in [202, 401, 429] for status in results)


class TestHealthAndMonitoring:
    """Test health check and monitoring workflows."""

    def test_health_check_workflow(self, client: TestClient):
        """Test health check workflow."""
        response = client.get("/health")

        assert response.status_code == 200
        data = response.json()

        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

    def test_ping_workflow(self, client: TestClient):
        """Test ping workflow."""
        response = client.post("/ping")

        assert response.status_code == 200
        data = response.json()

        assert data["message"] == "pong"

    def test_metrics_workflow(self, client: TestClient):
        """Test metrics endpoint workflow."""
        response = client.get("/metrics")

        # Metrics might not be available in test environment
        assert response.status_code in [200, 404]


class TestDataPersistence:
    """Test data persistence across requests."""

    def test_task_persistence(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test that task data persists across requests."""
        # Create a task
        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        if response.status_code != 202:
            pytest.skip("Subtitle endpoint not available")

        data = response.json()
        task_id = data["task_id"]

        # Check task status multiple times
        for attempt in range(3):
            status_response = client.get(f"/api/task/{task_id}", headers=auth_headers)

            if status_response.status_code == 200:
                status_data = status_response.json()
                assert status_data["task_id"] == task_id
                # Task should persist across requests

            time.sleep(0.5)


class TestIntegrationWithExternalServices:
    """Test integration with external services (mocked)."""

    @patch("worker.subtitles.youtube.YouTubeSubtitleDownloader")
    def test_youtube_integration_mock(
        self, mock_downloader, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test YouTube integration with mocked service."""
        # Mock successful response
        mock_instance = mock_downloader.return_value
        mock_instance.download_subtitles.return_value = {
            "title": "Mocked Video Title",
            "original_language": "en",
            "en_subtitles": "Mocked English subtitles",
            "ru_subtitles": "Mocked Russian subtitles",
        }

        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        if response.status_code == 202:
            data = response.json()
            task_id = data["task_id"]

            # Poll for completion
            max_attempts = 5
            for attempt in range(max_attempts):
                status_response = client.get(
                    f"/api/task/{task_id}", headers=auth_headers
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()

                    if status_data["status"] == TaskStatus.COMPLETED:
                        # Verify mocked data
                        assert status_data["title"] == "Mocked Video Title"
                        break

                time.sleep(0.5)

    @patch("worker.summarizers.summarizer.TextSummarizer")
    def test_gemini_integration_mock(
        self, mock_summarizer, client: TestClient, sample_text_content, auth_headers
    ):
        """Test Gemini AI integration with mocked service."""
        # Mock successful response
        mock_instance = mock_summarizer.return_value
        mock_instance.summarize.return_value = "Mocked summary of the input text."

        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["medium"]},
            headers=auth_headers,
        )

        if response.status_code == 202:
            data = response.json()
            task_id = data["task_id"]

            # Poll for completion
            max_attempts = 5
            for attempt in range(max_attempts):
                status_response = client.get(
                    f"/api/task/{task_id}", headers=auth_headers
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()

                    if status_data["status"] == TaskStatus.COMPLETED:
                        # Verify mocked data
                        assert "Mocked summary" in status_data["summary"]
                        break

                time.sleep(0.5)
