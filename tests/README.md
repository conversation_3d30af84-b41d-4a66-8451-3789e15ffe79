# Тесты для YouTube Subtitles API

Этот каталог содержит комплексный набор тестов для FastAPI сервера извлечения и суммаризации субтитров YouTube.

## Структура тестов

```
tests/
├── conftest.py                 # Pytest fixtures и конфигурация
├── unit/                       # Модульные тесты
│   ├── test_models.py         # Тесты моделей и валидации
│   ├── test_config.py         # Тесты конфигурации
│   ├── test_services.py       # Тесты сервисов
│   └── test_utils.py          # Тесты утилит
├── integration/                # Интеграционные тесты
│   ├── test_api.py            # Тесты API endpoints
│   ├── test_auth.py           # Тесты аутентификации
│   ├── test_websockets.py     # Тесты WebSocket
│   └── test_database.py       # Тесты базы данных
├── functional/                 # Функциональные тесты
│   ├── test_task_queue.py     # Тесты системы очередей
│   └── test_middleware.py     # Тесты middleware
├── e2e/                       # End-to-end тесты
│   └── test_e2e.py            # Полные сценарии
└── performance/               # Тесты производительности
    └── test_performance.py    # Нагрузочные тесты
```

## Запуск тестов

### Все тесты
```bash
uv run python -m pytest
```

### По категориям
```bash
# Только unit тесты
uv run python -m pytest tests/unit/

# Только integration тесты
uv run python -m pytest tests/integration/

# Только функциональные тесты
uv run python -m pytest tests/functional/

# Только e2e тесты
uv run python -m pytest tests/e2e/
```

### По маркерам
```bash
# Только быстрые тесты (исключить медленные)
uv run python -m pytest -m "not slow"

# Только тесты аутентификации
uv run python -m pytest -m auth

# Только WebSocket тесты
uv run python -m pytest -m websocket

# Только тесты базы данных
uv run python -m pytest -m database
```

### С покрытием кода
```bash
# Установить pytest-cov
uv add pytest-cov

# Запустить с покрытием
uv run python -m pytest --cov=. --cov-report=html --cov-report=term-missing
```

### Параллельный запуск
```bash
# Установить pytest-xdist
uv add pytest-xdist

# Запустить параллельно
uv run python -m pytest -n auto
```

### Подробный вывод
```bash
# Подробный вывод с логами
uv run python -m pytest -v -s

# Показать самые медленные тесты
uv run python -m pytest --durations=10

# Остановиться на первой ошибке
uv run python -m pytest -x
```

# Пример запуска
```bash
# Все тесты
uv run python -m pytest

# Только unit тесты
uv run python -m pytest tests/unit/

# С покрытием кода
uv run python -m pytest --cov=. --cov-report=html

# Параллельно
uv run python -m pytest -n auto

# Конкретный тест
uv run python -m pytest tests/unit/test_models.py::TestValidators::test_validate_youtube_url_valid -v
```bash

## Конфигурация тестов

### Переменные окружения для тестов
```bash
# Отключить аутентификацию
export REQUIRE_AUTH=false

# Отключить rate limiting
export RATE_LIMITING_ENABLED=false

# Использовать тестовую базу данных
export DATABASE_TYPE=sqlite

# Уровень логирования
export LOG_LEVEL=DEBUG

# Отключить внешние сервисы
export PROMETHEUS_ENABLED=false
export SECURITY_HEADERS_ENABLED=false
```

### Создание .env.test файла
```bash
# Создать файл .env.test
cat > .env.test << EOF
REQUIRE_AUTH=false
RATE_LIMITING_ENABLED=false
DATABASE_TYPE=sqlite
LOG_LEVEL=DEBUG
STRUCTURED_LOGGING=false
PROMETHEUS_ENABLED=false
SECURITY_HEADERS_ENABLED=false
EOF
```

## Типы тестов

### Unit тесты (tests/unit/)
- **test_models.py**: Тестирование Pydantic моделей и валидации
- **test_config.py**: Тестирование конфигурации приложения
- **test_services.py**: Тестирование бизнес-логики сервисов
- **test_utils.py**: Тестирование утилитарных функций

### Integration тесты (tests/integration/)
- **test_api.py**: Тестирование REST API endpoints
- **test_auth.py**: Тестирование системы аутентификации
- **test_websockets.py**: Тестирование WebSocket соединений
- **test_database.py**: Тестирование работы с базой данных

### Functional тесты (tests/functional/)
- **test_task_queue.py**: Тестирование системы очередей задач
- **test_middleware.py**: Тестирование middleware компонентов

### E2E тесты (tests/e2e/)
- **test_e2e.py**: Полные сценарии использования приложения

## Fixtures и моки

### Основные fixtures (conftest.py)
- `client`: TestClient для HTTP запросов
- `async_client`: AsyncClient для асинхронных запросов
- `auth_headers`: Заголовки аутентификации
- `test_db`: Тестовая база данных
- `mock_task_queue`: Мок системы очередей
- `sample_youtube_urls`: Тестовые YouTube URLs
- `sample_text_content`: Тестовый текстовый контент
- `sample_ttml_content`: Тестовый TTML контент

### Моки внешних сервисов
- `mock_youtube_downloader`: Мок YouTube загрузчика
- `mock_text_summarizer`: Мок текстового суммаризатора
- `mock_gemini_api`: Мок Gemini AI API

## Отладка тестов

### Запуск отдельного теста
```bash
# Запустить конкретный тест
uv run python -m pytest tests/unit/test_models.py::TestValidators::test_validate_youtube_url_valid -v

# Запустить тесты в конкретном классе
uv run python -m pytest tests/integration/test_api.py::TestHealthEndpoints -v
```

### Отладка с pdb
```bash
# Запустить с отладчиком
uv run python -m pytest --pdb

# Остановиться на первой ошибке с отладчиком
uv run python -m pytest --pdb -x
```

### Логирование в тестах
```python
import logging
logging.basicConfig(level=logging.DEBUG)

def test_something():
    logger = logging.getLogger(__name__)
    logger.debug("Debug message in test")
```

## Continuous Integration

### GitHub Actions пример
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    - name: Install uv
      run: pip install uv
    - name: Install dependencies
      run: uv sync
    - name: Run tests
      run: uv run python -m pytest --cov=. --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## Лучшие практики

### Именование тестов
- Используйте описательные имена: `test_create_subtitle_task_with_valid_url`
- Группируйте тесты в классы: `TestSubtitleEndpoints`
- Используйте маркеры для категоризации

### Структура тестов
```python
def test_something():
    # Arrange - подготовка данных
    data = {"key": "value"}
    
    # Act - выполнение действия
    result = function_under_test(data)
    
    # Assert - проверка результата
    assert result == expected_value
```

### Изоляция тестов
- Каждый тест должен быть независимым
- Используйте fixtures для подготовки данных
- Очищайте состояние после тестов

### Моки и заглушки
- Мокайте внешние зависимости
- Используйте реальные объекты для unit тестов
- Тестируйте интеграцию с реальными сервисами в integration тестах

## Покрытие кода

### Цели покрытия
- **Unit тесты**: 90%+ покрытие
- **Integration тесты**: Покрытие критических путей
- **E2E тесты**: Покрытие основных сценариев

### Исключения из покрытия
- Файлы конфигурации
- Миграции базы данных
- Внешние библиотеки
- Код для разработки/отладки

## Производительность тестов

### Оптимизация времени выполнения
- Используйте параллельный запуск (`pytest-xdist`)
- Группируйте медленные тесты с маркером `@pytest.mark.slow`
- Используйте моки вместо реальных внешних вызовов
- Оптимизируйте fixtures (scope='session' для дорогих операций)

### Мониторинг производительности
```bash
# Показать самые медленные тесты
uv run python -m pytest --durations=0

# Профилирование тестов
uv run python -m pytest --profile
```
