#!/usr/bin/env python3
"""
Test script for the updated summarize_files.py client with authentication.
"""

import subprocess
import sys
import tempfile
from pathlib import Path


def create_test_files(test_dir: Path) -> list[Path]:
    """Create test text files for processing."""
    test_files = []

    # Test file 1: Short text
    file1 = test_dir / "test1.txt"
    file1.write_text(
        "Это короткий тестовый текст для проверки работы системы суммаризации. "
        "Он содержит несколько предложений для демонстрации функциональности. "
        "Система должна создать краткое резюме этого содержимого."
    )
    test_files.append(file1)

    # Test file 2: Longer text
    file2 = test_dir / "test2.txt"
    file2.write_text(
        "Это более длинный тестовый документ, который содержит больше информации. "
        "В нем несколько абзацев текста, которые описывают различные аспекты тестирования. "
        "Первый абзац рассказывает о важности тестирования программного обеспечения. "
        "Второй абзац объясняет, как автоматизированные тесты помогают обеспечить качество. "
        "Третий абзац описывает различные типы тестов: модульные, интеграционные и системные. "
        "Четвертый абзац подчеркивает важность непрерывной интеграции и развертывания. "
        "Пятый абзац заключает, что тестирование является критически важной частью разработки."
    )
    test_files.append(file2)

    # Test file 3: Technical text
    file3 = test_dir / "technical.txt"
    file3.write_text(
        "WebSocket - это протокол связи, который обеспечивает полнодуплексную связь "
        "между клиентом и сервером через одно TCP-соединение. "
        "В отличие от HTTP, WebSocket позволяет серверу инициировать передачу данных клиенту. "
        "Это делает его идеальным для приложений реального времени, таких как чаты, игры и торговые платформы. "
        "Протокол начинается с HTTP-запроса на обновление соединения, "
        "после чего переключается на WebSocket протокол. "
        "Аутентификация WebSocket может осуществляться различными способами: "
        "через query параметры, заголовки Authorization или субпротоколы."
    )
    test_files.append(file3)

    return test_files


def run_client_test(test_dir: Path, api_key: str = None, mode: str = "default") -> bool:
    """Run the client with specified parameters."""
    print(
        f"\n🧪 Testing client with mode='{mode}', api_key={'***' if api_key else 'None'}"
    )

    # Build command
    cmd = [
        sys.executable,
        "client/summarize_files.py",
        str(test_dir),
        "--mode",
        mode,
        "--debug",
    ]

    if api_key:
        cmd.extend(["--api-key", api_key])

    print(f"📋 Command: {' '.join(cmd[:4])} ... {'--api-key ***' if api_key else ''}")

    try:
        # Run the client
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60,  # 1 minute timeout
        )

        if result.returncode == 0:
            print("✅ Client completed successfully")
            return True
        else:
            print(f"❌ Client failed with return code: {result.returncode}")
            if result.stderr:
                print(f"Error output: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("⏰ Client timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running client: {e}")
        return False


def check_output_files(test_dir: Path, mode: str = "default") -> bool:
    """Check if output files were created."""
    print(f"\n📁 Checking output files for mode '{mode}'...")

    expected_files = []
    for txt_file in test_dir.glob("*.txt"):
        if mode == "default":
            md_file = test_dir / f"{txt_file.stem}.md"
        else:
            md_file = test_dir / f"{txt_file.stem}-{mode}.md"
        expected_files.append(md_file)

    success = True
    for md_file in expected_files:
        if md_file.exists():
            size = md_file.stat().st_size
            print(f"✅ {md_file.name} created ({size} bytes)")
        else:
            print(f"❌ {md_file.name} not found")
            success = False

    return success


def main():
    """Main test function."""
    print("🚀 Testing summarize_files.py client with authentication")
    print("=" * 60)

    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        print(f"📂 Test directory: {test_dir}")

        # Create test files
        test_files = create_test_files(test_dir)
        print(f"📝 Created {len(test_files)} test files:")
        for file in test_files:
            print(f"   - {file.name} ({file.stat().st_size} bytes)")

        # Test scenarios
        scenarios = [
            {
                "name": "Without API key",
                "api_key": None,
                "mode": "default",
                "description": "Should work if REQUIRE_AUTH=false, fail if REQUIRE_AUTH=true",
            },
            {
                "name": "With demo admin key",
                "api_key": "admin_key_67890",
                "mode": "concise",
                "description": "Should work if demo keys are available",
            },
            {
                "name": "With invalid key",
                "api_key": "invalid_key_123",
                "mode": "default",
                "description": "Should fail with authentication error",
            },
        ]

        results = []

        for i, scenario in enumerate(scenarios, 1):
            print(f"\n{'=' * 20} Scenario {i}: {scenario['name']} {'=' * 20}")
            print(f"📋 {scenario['description']}")

            # Run the test
            success = run_client_test(test_dir, scenario["api_key"], scenario["mode"])

            if success:
                # Check if output files were created
                output_success = check_output_files(test_dir, scenario["mode"])
                results.append(
                    {
                        "scenario": scenario["name"],
                        "client_success": True,
                        "output_success": output_success,
                    }
                )
            else:
                results.append(
                    {
                        "scenario": scenario["name"],
                        "client_success": False,
                        "output_success": False,
                    }
                )

        # Print summary
        print(f"\n{'=' * 60}")
        print("📊 Test Results Summary:")
        print(f"{'=' * 60}")

        for result in results:
            status = "✅" if result["client_success"] else "❌"
            output_status = "✅" if result["output_success"] else "❌"
            print(f"{status} {result['scenario']:<20} | Output: {output_status}")

        # Overall result
        successful_tests = sum(1 for r in results if r["client_success"])
        total_tests = len(results)

        print(
            f"\n🎯 Overall: {successful_tests}/{total_tests} scenarios completed successfully"
        )

        if successful_tests > 0:
            print("\n💡 Tips:")
            print(
                "   - If authentication tests fail, check server REQUIRE_AUTH setting"
            )
            print("   - Make sure the server is running: python main.py")
            print("   - Check demo keys are available in server logs")
            print("   - Use --debug flag for detailed client logs")
            print("\n🎨 Expected output formatting:")
            print("   🔐 With API key: magenta colored authentication message")
            print("   🔓 Without API key: yellow colored authentication message")

        return successful_tests == total_tests


if __name__ == "__main__":
    print("📋 Requirements:")
    print("   - Server should be running on localhost:8000")
    print("   - Run from project root directory")
    print("   - Make sure client/summarize_files.py exists")
    print()

    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
