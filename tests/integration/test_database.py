"""
Integration tests for database functionality.
"""

import pytest
from sqlalchemy import create_engine, text

from models.database import Base, DatabaseManager, Subtitles, Tasks


class TestDatabaseConnection:
    """Test database connection functionality."""

    def test_database_manager_creation(self):
        """Test DatabaseManager can be created."""
        manager = DatabaseManager()
        assert isinstance(manager, DatabaseManager)

    def test_sqlite_connection(self):
        """Test SQLite database connection."""
        # Create test database
        test_db_url = "sqlite:///test_connection.db"
        engine = create_engine(test_db_url, echo=False)

        # Test connection
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1

        # Cleanup
        import os

        if os.path.exists("test_connection.db"):
            os.remove("test_connection.db")

    def test_database_initialization(self):
        """Test database initialization."""
        manager = DatabaseManager()

        # Should initialize without errors
        assert manager.engine is not None

    def test_database_tables_creation(self):
        """Test database tables are created."""
        # Create test database
        test_db_url = "sqlite:///test_tables.db"
        engine = create_engine(test_db_url, echo=False)

        # Create tables
        Base.metadata.create_all(bind=engine)

        # Check tables exist
        with engine.connect() as connection:
            # Check subtitles table
            result = connection.execute(
                text(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='subtitles'"
                )
            )
            assert result.fetchone() is not None

            # Check tasks table
            result = connection.execute(
                text(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='tasks'"
                )
            )
            assert result.fetchone() is not None

        # Cleanup
        import os

        if os.path.exists("test_tables.db"):
            os.remove("test_tables.db")


class TestSubtitlesModel:
    """Test Subtitles model functionality."""

    def test_subtitles_model_creation(self, test_db):
        """Test creating Subtitles model instance."""
        subtitle = Subtitles(
            video_id="test_video_123",
            title="Test Video Title",
            original_language="en",
            publish_date="2023-01-01",
            en_subtitles="English subtitle content",
            ru_subtitles="Russian subtitle content",
        )

        assert subtitle.video_id == "test_video_123"
        assert subtitle.title == "Test Video Title"
        assert subtitle.original_language == "en"
        assert subtitle.publish_date == "2023-01-01"
        assert subtitle.en_subtitles == "English subtitle content"
        assert subtitle.ru_subtitles == "Russian subtitle content"

    def test_subtitles_model_save(self, test_db):
        """Test saving Subtitles model to database."""
        subtitle = Subtitles(
            video_id="test_video_save",
            title="Test Save Video",
            original_language="en",
            en_subtitles="Test content",
        )

        # Save to database
        test_db.add(subtitle)
        test_db.commit()

        # Retrieve from database
        saved_subtitle = (
            test_db.query(Subtitles).filter_by(video_id="test_video_save").first()
        )
        assert saved_subtitle is not None
        assert saved_subtitle.title == "Test Save Video"

    def test_subtitles_model_update(self, test_db):
        """Test updating Subtitles model."""
        # Create and save subtitle
        subtitle = Subtitles(
            video_id="test_video_update", title="Original Title", original_language="en"
        )
        test_db.add(subtitle)
        test_db.commit()

        # Update subtitle
        subtitle.title = "Updated Title"
        subtitle.ru_subtitles = "Added Russian subtitles"
        test_db.commit()

        # Verify update
        updated_subtitle = (
            test_db.query(Subtitles).filter_by(video_id="test_video_update").first()
        )
        assert updated_subtitle.title == "Updated Title"
        assert updated_subtitle.ru_subtitles == "Added Russian subtitles"

    def test_subtitles_model_delete(self, test_db):
        """Test deleting Subtitles model."""
        # Create and save subtitle
        subtitle = Subtitles(video_id="test_video_delete", title="To Be Deleted")
        test_db.add(subtitle)
        test_db.commit()

        # Delete subtitle
        test_db.delete(subtitle)
        test_db.commit()

        # Verify deletion
        deleted_subtitle = (
            test_db.query(Subtitles).filter_by(video_id="test_video_delete").first()
        )
        assert deleted_subtitle is None

    def test_subtitles_model_constraints(self, test_db):
        """Test Subtitles model constraints."""
        # Test primary key constraint
        subtitle1 = Subtitles(video_id="duplicate_id", title="First")
        test_db.add(subtitle1)
        test_db.commit()

        # Try to add duplicate video_id
        subtitle2 = Subtitles(video_id="duplicate_id", title="Second")
        test_db.add(subtitle2)

        with pytest.raises(Exception):
            test_db.commit()

        test_db.rollback()


class TestTasksModel:
    """Test Tasks model functionality."""

    def test_tasks_model_creation(self, test_db):
        """Test creating Tasks model instance."""
        task = Tasks(
            task_id="test_task_123",
            task_type="subtitle",
            status="pending",
            input_data='{"url": "https://youtube.com/watch?v=test"}',
            result_data=None,
            error_message=None,
        )

        assert task.task_id == "test_task_123"
        assert task.task_type == "subtitle"
        assert task.status == "pending"
        assert '"url"' in task.input_data

    def test_tasks_model_save(self, test_db):
        """Test saving Tasks model to database."""
        task = Tasks(
            task_id="test_task_save",
            task_type="summarize",
            status="processing",
            input_data='{"text": "test content"}',
        )

        # Save to database
        test_db.add(task)
        test_db.commit()

        # Retrieve from database
        saved_task = test_db.query(Tasks).filter_by(task_id="test_task_save").first()
        assert saved_task is not None
        assert saved_task.task_type == "summarize"
        assert saved_task.status == "processing"

    def test_tasks_model_status_update(self, test_db):
        """Test updating task status."""
        # Create and save task
        task = Tasks(
            task_id="test_task_status",
            task_type="subtitle",
            status="pending",
            input_data='{"url": "test"}',
        )
        test_db.add(task)
        test_db.commit()

        # Update status
        task.status = "completed"
        task.result_data = '{"title": "Test Video", "subtitles": "content"}'
        test_db.commit()

        # Verify update
        updated_task = (
            test_db.query(Tasks).filter_by(task_id="test_task_status").first()
        )
        assert updated_task.status == "completed"
        assert updated_task.result_data is not None

    def test_tasks_model_error_handling(self, test_db):
        """Test task error handling."""
        # Create task with error
        task = Tasks(
            task_id="test_task_error",
            task_type="subtitle",
            status="failed",
            input_data='{"url": "invalid"}',
            error_message="Invalid URL provided",
        )
        test_db.add(task)
        test_db.commit()

        # Verify error storage
        error_task = test_db.query(Tasks).filter_by(task_id="test_task_error").first()
        assert error_task.status == "failed"
        assert error_task.error_message == "Invalid URL provided"


class TestDatabaseQueries:
    """Test database query functionality."""

    def test_query_subtitles_by_video_id(self, test_db):
        """Test querying subtitles by video ID."""
        # Create test data
        subtitle = Subtitles(
            video_id="query_test_123", title="Query Test Video", original_language="en"
        )
        test_db.add(subtitle)
        test_db.commit()

        # Query by video ID
        result = test_db.query(Subtitles).filter_by(video_id="query_test_123").first()
        assert result is not None
        assert result.title == "Query Test Video"

    def test_query_tasks_by_status(self, test_db):
        """Test querying tasks by status."""
        # Create test tasks
        task1 = Tasks(
            task_id="task1", task_type="subtitle", status="pending", input_data="{}"
        )
        task2 = Tasks(
            task_id="task2", task_type="summarize", status="completed", input_data="{}"
        )
        task3 = Tasks(
            task_id="task3", task_type="subtitle", status="pending", input_data="{}"
        )

        test_db.add_all([task1, task2, task3])
        test_db.commit()

        # Query pending tasks
        pending_tasks = test_db.query(Tasks).filter_by(status="pending").all()
        assert len(pending_tasks) == 2

        # Query completed tasks
        completed_tasks = test_db.query(Tasks).filter_by(status="completed").all()
        assert len(completed_tasks) == 1

    def test_query_tasks_by_type(self, test_db):
        """Test querying tasks by type."""
        # Create test tasks
        task1 = Tasks(
            task_id="task_type1",
            task_type="subtitle",
            status="pending",
            input_data="{}",
        )
        task2 = Tasks(
            task_id="task_type2",
            task_type="summarize",
            status="pending",
            input_data="{}",
        )
        task3 = Tasks(
            task_id="task_type3",
            task_type="subtitle",
            status="completed",
            input_data="{}",
        )

        test_db.add_all([task1, task2, task3])
        test_db.commit()

        # Query subtitle tasks
        subtitle_tasks = test_db.query(Tasks).filter_by(task_type="subtitle").all()
        assert len(subtitle_tasks) == 2

        # Query summarize tasks
        summarize_tasks = test_db.query(Tasks).filter_by(task_type="summarize").all()
        assert len(summarize_tasks) == 1

    def test_query_recent_tasks(self, test_db):
        """Test querying recent tasks."""
        from datetime import datetime, timedelta

        # Create tasks with different timestamps
        now = datetime.utcnow()
        old_time = now - timedelta(days=1)

        task1 = Tasks(
            task_id="recent1", task_type="subtitle", status="completed", input_data="{}"
        )
        task1.created_at = now

        task2 = Tasks(
            task_id="old1", task_type="subtitle", status="completed", input_data="{}"
        )
        task2.created_at = old_time

        test_db.add_all([task1, task2])
        test_db.commit()

        # Query recent tasks (last hour)
        recent_cutoff = now - timedelta(hours=1)
        recent_tasks = (
            test_db.query(Tasks).filter(Tasks.created_at > recent_cutoff).all()
        )

        # Should find the recent task
        assert len(recent_tasks) >= 1
        assert any(task.task_id == "recent1" for task in recent_tasks)


class TestDatabasePerformance:
    """Test database performance characteristics."""

    def test_bulk_insert_subtitles(self, test_db):
        """Test bulk inserting subtitles."""
        # Create multiple subtitles
        subtitles = []
        for i in range(10):
            subtitle = Subtitles(
                video_id=f"bulk_test_{i}",
                title=f"Bulk Test Video {i}",
                original_language="en",
            )
            subtitles.append(subtitle)

        # Bulk insert
        test_db.add_all(subtitles)
        test_db.commit()

        # Verify all were inserted
        count = (
            test_db.query(Subtitles)
            .filter(Subtitles.video_id.like("bulk_test_%"))
            .count()
        )
        assert count == 10

    def test_bulk_insert_tasks(self, test_db):
        """Test bulk inserting tasks."""
        # Create multiple tasks
        tasks = []
        for i in range(20):
            task = Tasks(
                task_id=f"bulk_task_{i}",
                task_type="subtitle" if i % 2 == 0 else "summarize",
                status="pending",
                input_data=f'{{"test": {i}}}',
            )
            tasks.append(task)

        # Bulk insert
        test_db.add_all(tasks)
        test_db.commit()

        # Verify all were inserted
        count = test_db.query(Tasks).filter(Tasks.task_id.like("bulk_task_%")).count()
        assert count == 20

    def test_query_performance(self, test_db):
        """Test query performance with indexed fields."""
        # Create test data
        for i in range(50):
            subtitle = Subtitles(
                video_id=f"perf_test_{i}",
                title=f"Performance Test {i}",
                original_language="en" if i % 2 == 0 else "ru",
            )
            test_db.add(subtitle)

        test_db.commit()

        # Test indexed query (by primary key)
        result = test_db.query(Subtitles).filter_by(video_id="perf_test_25").first()
        assert result is not None
        assert result.title == "Performance Test 25"

        # Test filtered query
        en_subtitles = test_db.query(Subtitles).filter_by(original_language="en").all()
        assert len(en_subtitles) == 25


class TestDatabaseTransactions:
    """Test database transaction handling."""

    def test_transaction_commit(self, test_db):
        """Test transaction commit."""
        # Start transaction
        subtitle = Subtitles(video_id="transaction_test", title="Transaction Test")
        test_db.add(subtitle)

        # Commit transaction
        test_db.commit()

        # Verify data is persisted
        result = test_db.query(Subtitles).filter_by(video_id="transaction_test").first()
        assert result is not None

    def test_transaction_rollback(self, test_db):
        """Test transaction rollback."""
        # Start transaction
        subtitle = Subtitles(video_id="rollback_test", title="Rollback Test")
        test_db.add(subtitle)

        # Rollback transaction
        test_db.rollback()

        # Verify data is not persisted
        result = test_db.query(Subtitles).filter_by(video_id="rollback_test").first()
        assert result is None

    def test_transaction_error_handling(self, test_db):
        """Test transaction error handling."""
        try:
            # Create invalid data that should cause error
            subtitle1 = Subtitles(video_id="error_test", title="First")
            test_db.add(subtitle1)
            test_db.commit()

            # Try to add duplicate (should fail)
            subtitle2 = Subtitles(video_id="error_test", title="Duplicate")
            test_db.add(subtitle2)
            test_db.commit()

        except Exception:
            # Rollback on error
            test_db.rollback()

            # Verify first record still exists
            result = test_db.query(Subtitles).filter_by(video_id="error_test").first()
            assert result is not None
            assert result.title == "First"
