#!/usr/bin/env python3
"""
Simple test script for WebSocket authentication.
"""

import asyncio
import json

import websockets
from websockets.exceptions import ConnectionClosedError


async def test_websocket_no_auth():
    """Test WebSocket connection without authentication."""
    print("🔓 Testing WebSocket without authentication...")

    try:
        uri = "ws://localhost:8000/ws/subtitles"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected without authentication")
            await websocket.close()
    except ConnectionClosedError as e:
        print(f"❌ Connection closed: code={e.code}, reason={e.reason}")
    except Exception as e:
        print(f"❌ Connection failed: {e}")


async def test_websocket_with_demo_key():
    """Test WebSocket connection with demo admin key."""
    print("\n🔑 Testing WebSocket with demo admin key...")

    # Demo admin key that should be available
    demo_key = "admin_key_67890"

    try:
        uri = f"ws://localhost:8000/ws/subtitles?token={demo_key}"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected with demo admin key")

            # Send a test message
            test_message = {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")

            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📥 Received: {data}")
            except TimeoutError:
                print("⏰ No response received within 5 seconds")

            await websocket.close()

    except ConnectionClosedError as e:
        print(f"❌ Connection closed: code={e.code}, reason={e.reason}")
    except Exception as e:
        print(f"❌ Connection failed: {e}")


async def test_websocket_with_invalid_key():
    """Test WebSocket connection with invalid key."""
    print("\n❌ Testing WebSocket with invalid key...")

    try:
        uri = "ws://localhost:8000/ws/subtitles?token=invalid_key_123"
        async with websockets.connect(uri) as websocket:
            print("❌ Should not have connected with invalid key")
            await websocket.close()
    except ConnectionClosedError as e:
        print(f"✅ Correctly rejected invalid key: code={e.code}, reason={e.reason}")
    except Exception as e:
        print(f"✅ Connection failed as expected: {e}")


async def test_websocket_summarize():
    """Test WebSocket summarize endpoint."""
    print("\n📝 Testing WebSocket summarize endpoint...")

    demo_key = "admin_key_67890"

    try:
        uri = f"ws://localhost:8000/ws/summarize?token={demo_key}"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to summarize endpoint")

            # Send a test message
            test_message = {
                "type": "text",
                "text": "This is a sample text that needs to be summarized. " * 5,
                "mode": "concise",
            }
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")

            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📥 Received: {data}")
            except TimeoutError:
                print("⏰ No response received within 5 seconds")

            await websocket.close()

    except ConnectionClosedError as e:
        print(f"❌ Connection closed: code={e.code}, reason={e.reason}")
    except Exception as e:
        print(f"❌ Connection failed: {e}")


async def main():
    """Run all tests."""
    print("🚀 WebSocket Authentication Test")
    print("=" * 40)
    print("Make sure the server is running on localhost:8000")
    print()

    try:
        # Test without authentication
        await test_websocket_no_auth()

        # Test with invalid key
        await test_websocket_with_invalid_key()

        # Test with demo key
        await test_websocket_with_demo_key()

        # Test summarize endpoint
        await test_websocket_summarize()

        print("\n✅ All tests completed!")

    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    print("📋 Requirements: pip install websockets")
    print("🔧 Make sure the server is running: python main.py")
    print()

    asyncio.run(main())
