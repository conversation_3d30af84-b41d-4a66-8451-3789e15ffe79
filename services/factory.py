"""Factory for creating and managing service instances."""

from worker.queue import TaskQueue

from .base import BaseService
from .subtitle_service import SubtitleService
from .summarize_service import SummarizeService
from .task_service import TaskService


class ServiceFactory:
    """Factory for creating and managing service instances."""

    _instance = None
    _services: dict[str, BaseService] = {}
    _initialized = False

    def __new__(cls, task_queue: TaskQueue | None = None):
        """Create a singleton instance of the service factory."""
        if cls._instance is None:
            if task_queue is None:
                raise ValueError("task_queue is required for the first initialization")

            cls._instance = super(ServiceFactory, cls).__new__(cls)
            cls._instance._task_queue = task_queue
            cls._instance._initialized = False

        return cls._instance

    def __init__(self, task_queue: TaskQueue | None = None):
        """Initialize the service factory."""
        # Only initialize once
        if self._initialized:
            return

        if task_queue is None:
            raise ValueError("task_queue is required")

        self._task_queue = task_queue
        self._initialize_services()
        self._initialized = True

    def _initialize_services(self) -> None:
        """Initialize all services."""
        # Subtitle service
        self._services["subtitle"] = SubtitleService(self._task_queue)

        # Summarize service
        self._services["summarize"] = SummarizeService(self._task_queue)

        # Task service
        self._services["task"] = TaskService(self._task_queue)

    def get_service(self, service_name: str) -> BaseService:
        """
        Get a service instance by name.

        Args:
            service_name: The name of the service to get.

        Returns:
            The service instance.

        Raises:
            ValueError: If the service name is invalid.
        """
        service = self._services.get(service_name.lower())
        if service is None:
            raise ValueError(f"Unknown service: {service_name}")

        return service

    @property
    def subtitle_service(self) -> SubtitleService:
        """Get the subtitle service."""
        return self.get_service("subtitle")

    @property
    def summarize_service(self) -> SummarizeService:
        """Get the summarize service."""
        return self.get_service("summarize")

    @property
    def task_service(self) -> TaskService:
        """Get the task service."""
        return self.get_service("task")

    @classmethod
    def get_instance(cls) -> "ServiceFactory":
        """
        Get the singleton instance of the service factory.

        Returns:
            The service factory instance.

        Raises:
            RuntimeError: If the factory has not been initialized.
        """
        if cls._instance is None:
            raise RuntimeError(
                "ServiceFactory has not been initialized. Call ServiceFactory(task_queue) first."
            )

        return cls._instance


# Global service factory instance
service_factory: ServiceFactory | None = None


def init_service_factory(task_queue) -> ServiceFactory:
    """
    Initialize the global service factory.

    Args:
        task_queue: The task queue to use for services.

    Returns:
        The initialized service factory.
    """
    global service_factory
    service_factory = ServiceFactory(task_queue)
    return service_factory


def get_service_factory() -> ServiceFactory:
    """
    Get the global service factory instance.

    Returns:
        The service factory instance.

    Raises:
        RuntimeError: If the factory has not been initialized.
    """
    if service_factory is None:
        raise RuntimeError(
            "ServiceFactory has not been initialized. Call init_service_factory() first."
        )

    return service_factory
