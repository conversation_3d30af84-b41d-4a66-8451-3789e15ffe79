"""Service for handling task-related operations."""

import logging
from typing import Any

from models.schemas import TaskStatus
from services.base import BaseService
from worker.queue import TaskQueue

logger = logging.getLogger(__name__)


class TaskService(BaseService):
    """Service for handling task-related operations."""

    def __init__(self, task_queue: TaskQueue):
        """
        Initialize the task service.

        Args:
            task_queue: The task queue for managing background tasks.
        """
        super().__init__()
        self.task_queue = task_queue

    async def get_task_status(self, task_id: str) -> dict[str, Any]:
        """
        Get the status of a task.

        Args:
            task_id: The ID of the task to check.

        Returns:
            Dict containing the task status and result if available.
        """
        self.logger.debug(f"Getting status for task: {task_id}")

        try:
            task_status = await self.task_queue.get_task_status(task_id)

            if task_status is None:
                return {
                    "status": TaskStatus.NOT_FOUND,
                    "error": f"Task {task_id} not found",
                }

            return {
                "status": task_status.status,
                "progress": task_status.progress,
                "result": task_status.result,
                "error": task_status.error,
                "created_at": (
                    task_status.created_at.isoformat()
                    if task_status.created_at
                    else None
                ),
                "updated_at": (
                    task_status.updated_at.isoformat()
                    if task_status.updated_at
                    else None
                ),
                "completed_at": (
                    task_status.completed_at.isoformat()
                    if task_status.completed_at
                    else None
                ),
            }

        except Exception as e:
            self.logger.error(
                f"Error getting task status for {task_id}: {e}", exc_info=True
            )
            return {
                "status": TaskStatus.FAILED,
                "error": f"Error getting task status: {str(e)}",
            }

    async def cancel_task(self, task_id: str) -> dict[str, Any]:
        """
        Cancel a running task.

        Args:
            task_id: The ID of the task to cancel.

        Returns:
            Dict containing the task ID and cancellation status.
        """
        self.logger.info(f"Cancelling task: {task_id}")

        try:
            success = await self.task_queue.cancel_task(task_id)

            if not success:
                return {
                    "task_id": task_id,
                    "status": "not_found",
                    "message": f"Task {task_id} not found or already completed",
                }

            return {
                "task_id": task_id,
                "status": "cancelled",
                "message": f"Task {task_id} cancelled successfully",
            }

        except Exception as e:
            self.logger.error(f"Error cancelling task {task_id}: {e}", exc_info=True)
            return {
                "task_id": task_id,
                "status": "error",
                "error": f"Error cancelling task: {str(e)}",
            }

    async def get_queue_status(self) -> dict[str, Any]:
        """
        Get the status of all task queues.

        Returns:
            Dict containing the status of all task queues.
        """
        self.logger.debug("Getting queue status")

        try:
            queue_sizes = self.task_queue.get_queue_sizes()
            active_tasks = self.task_queue.get_active_tasks()

            return {
                "queues": queue_sizes,
                "active_tasks": len(active_tasks),
                "status": "ok",
            }

        except Exception as e:
            self.logger.error(f"Error getting queue status: {e}", exc_info=True)
            return {"status": "error", "error": f"Error getting queue status: {str(e)}"}

    async def execute(self, *args: Any, **kwargs: Any) -> Any:
        """
        Execute the service operation.

        This is a generic method that routes to the appropriate method based on the operation.

        Args:
            *args: Positional arguments for the service.
            **kwargs: Keyword arguments for the service.

        Returns:
            The result of the service operation.
        """
        operation = kwargs.pop("operation", None)

        if operation == "get_task_status":
            return await self.get_task_status(*args, **kwargs)
        elif operation == "cancel_task":
            return await self.cancel_task(*args, **kwargs)
        elif operation == "get_queue_status":
            return await self.get_queue_status(*args, **kwargs)
        else:
            raise ValueError(f"Unknown operation: {operation}")
