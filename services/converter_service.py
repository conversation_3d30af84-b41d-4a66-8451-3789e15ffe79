"""Service for handling TTML to TXT conversion operations."""

import logging
from typing import Any

from models.schemas import ConvertTTMLRequest, ConvertTTMLResponse, TaskStatus
from services.base import BaseService
from worker.converters.ttml_converter import (
    TTMLConverter,
    TTMLParsingError,
    TTMLValidationError,
)

logger = logging.getLogger(__name__)


class ConverterService(BaseService):
    """Service for handling TTML to TXT conversion operations."""

    def __init__(self):
        """Initialize the converter service."""
        super().__init__()
        self.converter = TTMLConverter()

    async def convert_ttml_to_txt(
        self, request: ConvertTTMLRequest
    ) -> ConvertTTMLResponse:
        """
        Convert TTML content to TXT format.

        Args:
            request: The request containing the TTML content to convert.

        Returns:
            ConvertTTMLResponse: The response containing the converted TXT content or error.
        """
        self.logger.debug("Converting TTML to TXT")

        try:
            # Perform the conversion asynchronously to avoid blocking
            txt_content = await self.converter.convert_ttml_to_txt_async(
                request.ttml_text
            )

            self.logger.debug(
                f"TTML conversion completed successfully. "
                f"Output length: {len(txt_content)} characters"
            )

            return ConvertTTMLResponse(
                status=TaskStatus.COMPLETED, txt_content=txt_content
            )

        except TTMLValidationError as e:
            self.logger.warning(f"TTML validation error: {e}")
            return ConvertTTMLResponse(
                status=TaskStatus.FAILED, error=f"Invalid TTML format: {str(e)}"
            )

        except TTMLParsingError as e:
            self.logger.error(f"TTML parsing error: {e}")
            return ConvertTTMLResponse(
                status=TaskStatus.FAILED,
                error=f"Failed to parse TTML content: {str(e)}",
            )

        except ValueError as e:
            self.logger.warning(f"Value error during TTML conversion: {e}")
            return ConvertTTMLResponse(status=TaskStatus.FAILED, error=str(e))

        except Exception as e:
            self.logger.error(
                f"Unexpected error during TTML conversion: {e}", exc_info=True
            )
            return ConvertTTMLResponse(
                status=TaskStatus.FAILED,
                error="Internal server error during conversion",
            )

    async def execute(self, *args: Any, **kwargs: Any) -> ConvertTTMLResponse:
        """
        Execute the service operation.

        Args:
            *args: Positional arguments for the service.
            **kwargs: Keyword arguments for the service.

        Returns:
            The result of the service operation.
        """
        operation = kwargs.pop("operation", "convert_ttml_to_txt")

        if operation == "convert_ttml_to_txt":
            return await self.convert_ttml_to_txt(*args, **kwargs)
        else:
            raise ValueError(f"Unknown operation: {operation}")
