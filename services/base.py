"""Base service class with common functionality."""

import logging
from abc import ABC, abstractmethod
from typing import Any, Generic, TypeVar

from pydantic import BaseModel

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


class BaseService(ABC, Generic[T]):
    """Base service class with common functionality for all services."""

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @abstractmethod
    async def execute(self, *args: Any, **kwargs: Any) -> T:
        """
        Execute the service operation.

        Args:
            *args: Positional arguments for the service.
            **kwargs: Keyword arguments for the service.

        Returns:
            The result of the service operation.
        """

    async def _handle_error(
        self, error: Exception, context: str = "", raise_exception: bool = True
    ) -> None:
        """
        Handle errors that occur during service execution.

        Args:
            error: The exception that was raised.
            context: Additional context about where the error occurred.
            raise_exception: Whether to re-raise the exception after logging.

        Raises:
            The original exception if raise_exception is True.
        """
        error_msg = f"Error in {self.__class__.__name__}"
        if context:
            error_msg += f" ({context}): {str(error)}"
        else:
            error_msg += f": {str(error)}"

        self.logger.error(error_msg, exc_info=True)

        if raise_exception:
            raise error
