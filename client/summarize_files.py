import argparse
import asyncio
import logging
import sys
from pathlib import Path

from summarize_modules.api_client import APIClient
from summarize_modules.modes_client import get_available_modes, is_valid_mode
from rich.console import Console
from rich.progress import (
    BarColumn,
    Progress,
    SpinnerColumn,
    TaskProgressColumn,
    TextColumn,
)

# Конфигурация сервера по умолчанию
DEFAULT_HOST = "localhost"
DEFAULT_PORT = 8000


class FileSummarizer:
    def __init__(
        self,
        directory: str,
        mode: str = "default",
        host: str = DEFAULT_HOST,
        port: int = DEFAULT_PORT,
        format_markdown: bool = False,
        debug: bool = False,
        api_key: str = None,
    ):
        if not is_valid_mode(mode):
            raise ValueError(
                f"Режим должен быть одним из: {', '.join(get_available_modes())}"
            )

        self.directory = Path(directory).resolve()
        self.mode = mode
        self.format_markdown = format_markdown
        self.console = Console()

        # Создаем API клиент
        self.api_client = APIClient(host=host, port=port, api_key=api_key, debug=debug)

    async def start_server(self):
        """Запуск FastAPI сервера через API клиент"""
        await self.api_client.start_server()

    def cleanup(self):
        """Очистка ресурсов при завершении"""
        self.api_client.cleanup()

    async def process_file(
        self, file_path: Path, progress: Progress, task_id: int
    ) -> bool:
        """Обработка одного файла через API клиент"""
        return await self.api_client.process_file(
            file_path, self.mode, self.format_markdown, progress, task_id
        )

    async def process_directory(self):
        """Обработка всех файлов в директории"""
        self.api_client._log_debug(
            f"Scanning directory: {self.directory} for .txt files."
        )
        if not self.directory.exists() or not self.directory.is_dir():
            self.console.print(
                f"[red]Ошибка: Директория {self.directory} не существует[/red]"
            )
            self.api_client._log_debug(
                f"Directory {self.directory} does not exist or is not a directory."
            )
            return

        txt_files = list(self.directory.glob("**/*.txt"))
        self.api_client._log_debug(f"Found {len(txt_files)} .txt files to process.")
        if not txt_files:
            self.console.print("[yellow]Предупреждение: TXT файлы не найдены[/yellow]")
            return

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            # Добавляем общий прогресс
            main_task = progress.add_task(
                "[blue]Обработка файлов...[/blue]", total=len(txt_files)
            )

            # Обрабатываем каждый файл
            completed = 0
            for txt_file in txt_files:
                file_task = progress.add_task(
                    f"Подготовка: {txt_file.name}", total=None
                )
                if await self.process_file(txt_file, progress, file_task):
                    completed += 1
                progress.update(main_task, advance=1)
                progress.remove_task(file_task)

            # Выводим итоговую статистику
            self.console.print("\n[green]Обработка завершена![/green]")
            self.console.print(f"Всего файлов: {len(txt_files)}")
            self.console.print(f"Успешно обработано: {completed}")
            self.console.print(f"Пропущено/Ошибок: {len(txt_files) - completed}")


async def main():
    # Настраиваем парсер аргументов командной строки
    parser = argparse.ArgumentParser(description="Суммаризация текстовых файлов")
    parser.add_argument("directory", help="Директория с текстовыми файлами")
    parser.add_argument(
        "--mode",
        choices=get_available_modes(),
        default="default",
        help=f"Режим суммаризации ({', '.join(get_available_modes())})",
    )
    parser.add_argument(
        "--host",
        default=DEFAULT_HOST,
        help=f"Хост сервера (по умолчанию: {DEFAULT_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Порт сервера (по умолчанию: {DEFAULT_PORT})",
    )
    parser.add_argument(
        "--format",
        action="store_true",
        help="Форматировать Markdown текст с помощью mdformat",
    )
    parser.add_argument(
        "--totaltest",
        action="store_true",
        help="Запустить обработку каждого файла по всем доступным режимам",
    )
    parser.add_argument(  # Added --debug argument
        "--debug",
        action="store_true",
        help="Enable detailed debug logging on the client side.",
    )
    parser.add_argument(  # Added --api-key argument
        "--api-key",
        type=str,
        help="API key for WebSocket authentication (optional). If not provided, will try to connect without authentication.",
    )

    args = parser.parse_args()

    # Configure logging level based on --debug flag
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logger.debug("Debug mode enabled by command line flag.")

    try:
        # Запускаем сервер, передавая debug_mode и API ключ
        summarizer = FileSummarizer(
            args.directory,
            args.mode,
            args.host,
            args.port,
            args.format,
            debug=args.debug,
            api_key=args.api_key,  # Pass API key from command line
        )

        # Display authentication status with nice formatting
        console = Console()
        if args.api_key:
            console.print(
                f"[magenta]🔐 WebSocket аутентификация включена (API ключ: {args.api_key[:10]}***)[/magenta]"
            )
        else:
            console.print(
                "[blue]🔓 WebSocket аутентификация отключена (подключение без API ключа)[/blue]"
            )

        await summarizer.start_server()

        # Обрабатываем файлы
        if args.totaltest:
            # Обрабатываем каждый файл по всем доступным режимам
            self_cleanup = summarizer.cleanup
            summarizer.cleanup = lambda: None  # Временно отключаем cleanup

            from modes_client import AVAILABLE_MODES

            console = Console()
            console.print(
                f"[blue]Запуск тестирования по всем режимам ({len(AVAILABLE_MODES)} режимов)[/blue]"
            )

            total_modes = len(AVAILABLE_MODES)
            completed_modes = 0

            for mode in AVAILABLE_MODES:
                console.print(
                    f"[blue]Обработка файлов в режиме: {mode} ({completed_modes + 1}/{total_modes})[/blue]"
                )
                mode_summarizer = FileSummarizer(
                    args.directory,
                    mode,
                    args.host,
                    args.port,
                    args.format,
                    debug=args.debug,  # Pass debug flag
                    api_key=args.api_key,  # Pass API key
                )
                await mode_summarizer.process_directory()
                completed_modes += 1

            console.print("\n[green]Тестирование по всем режимам завершено![/green]")
            console.print(f"Всего обработано режимов: {completed_modes}/{total_modes}")

            if "self_cleanup" in locals():
                summarizer.cleanup = self_cleanup
        else:
            await summarizer.process_directory()

    except KeyboardInterrupt:
        print("\nПрерывание работы...")
        sys.exit(0)
    finally:
        if "summarizer" in locals() and hasattr(summarizer, "cleanup"):
            summarizer.cleanup()


if __name__ == "__main__":
    logger = logging.getLogger(__name__)
    asyncio.run(main())
