"""
Delimiter-based file splitter that splits files at specific delimiter patterns.
"""

import logging
import re

from .base import BaseSplitter, SplitConfig

logger = logging.getLogger(__name__)


class DelimiterSplitter(BaseSplitter):
    """Splitter that uses delimiter patterns to find split points."""

    # Pattern to match delimiters like "\n---\n" with optional whitespace
    DEFAULT_DELIMITER_PATTERN = r"\n\s*---\s*\n"

    def __init__(self, config: SplitConfig, delimiter_pattern: str = None):
        super().__init__(config)
        self.delimiter_pattern = delimiter_pattern or self.DEFAULT_DELIMITER_PATTERN

    def find_delimiter_positions(self, content: str) -> list[int]:
        """Find all positions of the delimiter in the content."""
        delimiter_positions = [0]  # Start position

        # Find all occurrences of the delimiter pattern
        for match in re.finditer(self.delimiter_pattern, content):
            delimiter_positions.append(match.start())

        # Add the end position
        delimiter_positions.append(len(content))

        return delimiter_positions

    def find_split_positions(self, content: str) -> list[int]:
        """Determine optimal split points based on delimiter positions and target size."""
        total_length = len(content)

        # Detect language and get appropriate size constants
        language = self.detect_language(content)
        max_file_size, target_part_size, max_part_size, min_part_size = (
            self.config.get_adjusted_sizes(language)
        )

        logger.info(f"Processing {language} text with delimiter-based splitting")
        logger.debug(
            f"Size constants - MAX_FILE_SIZE: {max_file_size}, "
            f"TARGET_PART_SIZE: {target_part_size}, MAX_PART_SIZE: {max_part_size}, "
            f"MIN_PART_SIZE: {min_part_size}"
        )

        # If the file is small enough, return it as is
        if total_length <= max_file_size:
            return [0, total_length]

        # Find delimiter positions
        delimiter_positions = self.find_delimiter_positions(content)
        logger.info(f"Found {len(delimiter_positions) - 2} delimiters in the file")

        # Skip the first and last positions (they're just the start and end)
        usable_delimiters = delimiter_positions[1:-1]

        # If we have no usable delimiters, we can't split the file
        if not usable_delimiters:
            logger.warning("No usable delimiters found, cannot split the file")
            return [0, total_length]

        return self._determine_optimal_splits(
            total_length,
            usable_delimiters,
            target_part_size,
            max_part_size,
            min_part_size,
        )

    def _determine_optimal_splits(
        self,
        total_length: int,
        usable_delimiters: list[int],
        target_part_size: int,
        max_part_size: int,
        min_part_size: int,
    ) -> list[int]:
        """Determine optimal split points from available delimiter positions."""
        # Calculate minimum number of parts needed to ensure no part exceeds max_part_size
        min_parts_needed = (
            total_length + max_part_size - 1
        ) // max_part_size  # Ceiling division

        # Calculate ideal number of parts based on target_part_size
        ideal_parts = max(
            min_parts_needed, (total_length + target_part_size - 1) // target_part_size
        )

        logger.debug(
            f"File size: {total_length}, minimum parts needed: {min_parts_needed}, "
            f"ideal parts: {ideal_parts}"
        )

        # Calculate the ideal part size
        ideal_part_size = total_length / ideal_parts

        # Strategy: Try to create roughly equal parts using only the delimiters
        split_points = [0]  # Start with the beginning of the file
        next_ideal_position = ideal_part_size

        while (
            next_ideal_position < total_length - min_part_size
        ):  # Ensure last part is not too small
            # Find the delimiter closest to the ideal position
            best_delimiter = None
            best_distance = float("inf")

            for pos in usable_delimiters:
                # Skip positions we've already passed
                if pos <= split_points[-1]:
                    continue

                # Skip positions that would create a part smaller than min_part_size
                if pos - split_points[-1] < min_part_size:
                    continue

                # Skip positions that would create a part larger than max_part_size
                if pos - split_points[-1] > max_part_size:
                    # If we can't find any delimiter within max_part_size, we'll have to use
                    # the closest one that doesn't exceed max_part_size
                    continue

                # Calculate distance to ideal position
                distance = abs(pos - (split_points[-1] + ideal_part_size))

                if distance < best_distance:
                    best_distance = distance
                    best_delimiter = pos

            # If we found a suitable delimiter, use it
            if best_delimiter is not None:
                split_points.append(best_delimiter)
                next_ideal_position = split_points[-1] + ideal_part_size
            else:
                # If we can't find a suitable delimiter, we have to stop
                # This means we can't split the file according to the requirements
                logger.warning("Could not find suitable delimiters for splitting")
                break

        # Add the end of the file if it's not already included
        if split_points[-1] != total_length:
            split_points.append(total_length)

        return split_points

    def _post_process_part(
        self, content: str, part_index: int, total_parts: int
    ) -> str:
        """Remove delimiters at the beginning and end of parts."""
        # Remove delimiter at the beginning of the part (except for the first part)
        if part_index > 0 and content.startswith("\n"):
            # Find the end of the delimiter pattern at the beginning
            match = re.match(r"^\n\s*---\s*\n", content)
            if match:
                content = content[match.end() :]
                logger.debug(
                    f"Removed delimiter at the beginning of part {part_index + 1}"
                )

        # Remove delimiter at the end of the part (except for the last part)
        if part_index < total_parts - 1 and content.endswith("\n"):
            # Find the start of the delimiter pattern at the end
            match = re.search(r"\n\s*---\s*\n$", content)
            if match:
                content = content[: match.start()] + "\n"
                logger.debug(f"Removed delimiter at the end of part {part_index + 1}")

        return content

    def split_file(self, file_path: str) -> bool:
        """Split a file into parts based on delimiter positions."""
        try:
            # Read file content
            with open(file_path, encoding="utf-8") as file:
                content = file.read()

            file_size = len(content)

            # Detect language and get appropriate size constants
            language = self.detect_language(content)
            max_file_size, target_part_size, max_part_size, min_part_size = (
                self.config.get_adjusted_sizes(language)
            )

            # Check if file needs splitting
            if file_size <= max_file_size:
                logger.info(
                    f"File {file_path} is smaller than {max_file_size} characters ({language} text), skipping."
                )
                return False

            logger.info(
                f"Processing file: {file_path} ({file_size} characters, {language} text)"
            )

            # Find split points using delimiter strategy
            split_points = self.find_split_positions(content)

            # Validate split points
            if not self.validate_split_points(split_points, file_size, max_part_size):
                logger.warning(f"Could not find suitable split points for {file_path}")
                return False

            # Additional validation for delimiter splitting: ensure split points are at delimiters
            delimiter_positions = self.find_delimiter_positions(content)
            for i in range(1, len(split_points) - 1):  # Skip start and end positions
                if split_points[i] not in delimiter_positions:
                    logger.warning(
                        f"Split point at position {split_points[i]} is not at a delimiter"
                    )
                    return False

            # Split the file and save parts
            return self._save_parts(file_path, content, split_points)

        except Exception as e:
            logger.error(f"Error splitting file {file_path}: {e}")
            return False

    def validate_split_points(
        self, split_points: list[int], content_length: int, max_part_size: int
    ) -> bool:
        """Validate that split points create acceptable parts."""
        return super().validate_split_points(
            split_points, content_length, max_part_size
        )
