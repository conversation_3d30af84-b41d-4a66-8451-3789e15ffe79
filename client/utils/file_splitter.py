#!/usr/bin/env python3
"""
Universal file splitter for large text files (MD/TXT).

This script can split large markdown and text files into smaller parts using different strategies:
- heuristic: Uses transition words and natural language patterns (good for subtitles)
- delim: Uses delimiter patterns like "---" (good for structured markdown)

The script automatically detects file types and applies appropriate splitting strategies.
Files are split when they exceed the specified size threshold (default: 250,000 characters).

Usage:
    python file_splitter.py [directory] --mode [heuristic|delim] --pattern "*.md"
    python file_splitter.py . --mode heuristic --pattern "*.txt"
    python file_splitter.py /path/to/files --mode delim --maxtarget 300000
"""

import argparse
import logging
import sys
from pathlib import Path

from splitters import (
    DelimiterSplitter,
    HeuristicSplitter,
    SplitConfig,
    process_directory,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def get_file_patterns_for_mode(mode: str) -> list[str]:
    """Get default file patterns based on the splitting mode."""
    if mode == "heuristic":
        return ["*.txt", "*.md"]  # Heuristic works well with both
    elif mode == "delim":
        return [
            "*.md",
            "*.txt",
        ]  # Delimiter is primarily for markdown but can work with txt
    else:
        return ["*.md", "*.txt"]


def create_splitter(
    mode: str, config: SplitConfig, delimiter_pattern: str | None = None
):
    """Create the appropriate splitter based on the mode."""
    if mode == "heuristic":
        return HeuristicSplitter(config)
    elif mode == "delim":
        return DelimiterSplitter(config, delimiter_pattern)
    else:
        raise ValueError(f"Unknown splitting mode: {mode}")


def validate_directory(directory: str) -> bool:
    """Validate that the directory exists and is accessible."""
    directory_path = Path(directory)
    if not directory_path.exists():
        logger.error(f"Directory {directory} does not exist")
        return False
    if not directory_path.is_dir():
        logger.error(f"{directory} is not a directory")
        return False
    return True


def main():
    """Main function to parse arguments and process files."""
    parser = argparse.ArgumentParser(
        description="Split large text files (MD/TXT) into smaller parts using different strategies.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Split MD files in current dir using delim mode
  %(prog)s --mode heuristic                   # Split using heuristic mode (good for subtitles)
  %(prog)s --mode delim --pattern "*.txt"     # Split TXT files using delimiter mode
  %(prog)s /path/to/files --maxtarget 300000  # Split files with 300k character threshold
  %(prog)s --delimiter-pattern "\\n===\\n"    # Use custom delimiter pattern
        """,
    )

    # Positional arguments
    parser.add_argument(
        "directory",
        nargs="?",
        default=".",
        help="Directory containing files to process (default: current directory)",
    )

    # Mode selection
    parser.add_argument(
        "--mode",
        choices=["heuristic", "delim"],
        default="delim",
        help="Splitting mode: 'heuristic' for transition words, 'delim' for delimiters (default: delim)",
    )

    # File selection
    parser.add_argument(
        "--pattern",
        help="File pattern to match (e.g., '*.md', '*.txt'). If not specified, uses mode defaults.",
    )

    # Size configuration
    parser.add_argument(
        "--maxtarget",
        type=int,
        default=250000,
        help="Maximum file size threshold for splitting in characters (default: 250000)",
    )

    parser.add_argument(
        "--english-multiplier",
        type=float,
        default=1.8,
        help="Size multiplier for English text detection (default: 1.8)",
    )

    # Delimiter configuration
    parser.add_argument(
        "--delimiter-pattern",
        help="Custom delimiter regex pattern for delim mode (default: \\n\\s*---\\s*\\n)",
    )

    # Logging
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    # Parse arguments
    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        logging.getLogger("splitters").setLevel(logging.DEBUG)

    # Validate directory
    if not validate_directory(args.directory):
        sys.exit(1)

    # Determine file pattern
    if args.pattern:
        patterns = [args.pattern]
    else:
        patterns = get_file_patterns_for_mode(args.mode)

    logger.info(f"Using splitting mode: {args.mode}")
    logger.info(f"Processing directory: {args.directory}")
    logger.info(f"File patterns: {patterns}")
    logger.info(f"Max file size: {args.maxtarget} characters")

    # Create configuration
    config = SplitConfig(
        max_file_size=args.maxtarget,
        english_multiplier=args.english_multiplier,
    )

    # Create splitter
    try:
        splitter = create_splitter(args.mode, config, args.delimiter_pattern)
    except ValueError as e:
        logger.error(str(e))
        sys.exit(1)

    # Process files for each pattern
    total_files_processed = 0
    total_files_split = 0

    for pattern in patterns:
        logger.info(f"Processing files matching pattern: {pattern}")
        files_processed, files_split = process_directory(
            splitter, args.directory, pattern
        )
        total_files_processed += files_processed
        total_files_split += files_split

        if files_processed > 0:
            logger.info(
                f"Pattern {pattern}: Processed {files_processed} files, split {files_split} files"
            )

    # Print final summary
    if total_files_processed == 0:
        logger.warning("No files were found matching the specified patterns")
    else:
        logger.info(
            f"Final Summary: Processed {total_files_processed} files, "
            f"split {total_files_split} files"
        )

        if total_files_split > 0:
            logger.info(
                "Split files have been saved with '_part1', '_part2', etc. suffixes"
            )


if __name__ == "__main__":
    main()
