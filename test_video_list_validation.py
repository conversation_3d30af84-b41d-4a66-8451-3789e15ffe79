#!/usr/bin/env python3
"""
Quick test script to validate our new YouTube video list URL validation.
"""

import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.schemas import VideoListRequest
from models.validators import validate_youtube_video_list_url


def test_video_list_validation():
    """Test the new video list URL validation."""

    # Test URLs that should be valid
    valid_urls = [
        "https://www.youtube.com/@ByteMonk/videos",
        "https://www.youtube.com/@ByteMonk",
        "https://www.youtube.com/channel/UC123456789012345678901/videos",
        "https://www.youtube.com/channel/UC123456789012345678901",
        "https://www.youtube.com/playlist?list=PLrAXtmRdnEQy4Q1234567890",
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy4Q1234567890",
        "https://www.youtube.com/c/ByteMonk/videos",
        "https://www.youtube.com/user/ByteMonk/videos",
    ]

    # Test URLs that should be invalid
    invalid_urls = [
        "https://example.com/video",
        "https://vimeo.com/123456789",
        "not_a_url",
        "",
        "https://www.youtube.com/invalid_path",
    ]

    print("Testing valid URLs:")
    for url in valid_urls:
        try:
            result = validate_youtube_video_list_url(url)
            print(f"✅ {url} -> Valid")
        except ValueError as e:
            print(f"❌ {url} -> Error: {e}")

    print("\nTesting invalid URLs:")
    for url in invalid_urls:
        try:
            result = validate_youtube_video_list_url(url)
            print(f"❌ {url} -> Should have failed but passed")
        except ValueError as e:
            print(f"✅ {url} -> Correctly rejected: {e}")

    print("\nTesting VideoListRequest model:")

    # Test with valid channel URL
    try:
        request = VideoListRequest(url="https://www.youtube.com/@ByteMonk/videos")
        print(f"✅ VideoListRequest with channel URL: {request.url}")
    except Exception as e:
        print(f"❌ VideoListRequest failed: {e}")

    # Test with invalid URL
    try:
        request = VideoListRequest(url="https://example.com/video")
        print(f"❌ VideoListRequest should have failed but passed: {request.url}")
    except Exception as e:
        print(f"✅ VideoListRequest correctly rejected invalid URL: {e}")


if __name__ == "__main__":
    test_video_list_validation()
