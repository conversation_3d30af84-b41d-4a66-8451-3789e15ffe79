import re
from enum import Enum

from pydantic import BaseModel, ConfigDict, Field, ValidationInfo, field_validator

# Динамическое создание SummarizeMode на основе доступных режимов в MODEL_CONFIGS
from worker.summarizers.config_loader import get_config_loader

# Import custom validators
from .validators import (
    ValidationConstants,
    client_uid_field,
    text_content_field,
    validate_client_uid,
    validate_filename,
    validate_language_code,
    validate_text_content,
    validate_youtube_url,
    youtube_url_field,
)


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class FileType(str, Enum):
    TXT = "text/plain"
    MD = "text/markdown"


class MessageType(str, Enum):
    STATUS = "status_update"
    RESULT = "result"
    TEXT = "text_input"
    FILE = "file_input"


class SubtitleRequest(BaseModel):
    """Request model for subtitle extraction with enhanced validation."""

    url: str = youtube_url_field()
    client_uid: str | None = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only

    @field_validator("url")
    @classmethod
    def validate_url(cls, v):
        """Validate that the URL is a proper YouTube URL."""
        return validate_youtube_url(v)

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "client_uid": "user_123",
            }
        }
    )


class SubtitleResponse(BaseModel):
    """Response model for subtitle extraction with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: str | None = client_uid_field()
    title: str | None = Field(None, max_length=500, description="Video title")
    original_language: str | None = Field(None, description="ISO 639-1 language code")
    publish_date: str | None = Field(None, description="Video publish date")
    en_subtitles: str | None = Field(None, description="English subtitles")
    ru_subtitles: str | None = Field(None, description="Russian subtitles")
    error: str | None = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @field_validator("original_language")
    @classmethod
    def validate_language(cls, v):
        """Validate language code format."""
        return validate_language_code(v)

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class VideoListRequest(BaseModel):
    """Request model for video list extraction with enhanced validation."""

    url: str = youtube_url_field()
    client_uid: str | None = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only

    @field_validator("url")
    @classmethod
    def validate_url(cls, v):
        """Validate that the URL is a proper YouTube URL."""
        return validate_youtube_url(v)

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class VideoListResponse(BaseModel):
    """Response model for video list extraction with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: str | None = client_uid_field()
    video_ids: list[str] | None = Field(None, description="List of extracted video IDs")
    error: str | None = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints."""
        return validate_client_uid(v)

    @field_validator("video_ids")
    @classmethod
    def validate_video_ids(cls, v):
        """Validate video IDs format."""
        if v is None:
            return v

        if not isinstance(v, list):
            raise ValueError("video_ids must be a list")

        if len(v) > 1000:  # Reasonable limit
            raise ValueError("Too many video IDs (max 1000)")

        # Validate each video ID format
        for video_id in v:
            if not isinstance(video_id, str):
                raise ValueError("All video IDs must be strings")
            if not re.match(r"^[a-zA-Z0-9_-]{11}$", video_id):
                raise ValueError(f"Invalid video ID format: {video_id}")

        return v


class SummarizeMode(str, Enum):
    # Автоматически создаем элементы Enum из ключей MODEL_CONFIGS
    # Используем dict comprehension для создания атрибутов
    locals().update({key.upper(): key for key in get_config_loader().available_modes})


class SummarizeRequest(BaseModel):
    """Request model for text summarization with enhanced validation."""

    client_uid: str | None = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    mode: SummarizeMode | None = Field(None, description="Summarization mode")
    og_text: str = text_content_field(
        min_length=10,
        max_length=500_000,  # 500KB of text
        description="Original text to summarize",
    )

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    @field_validator("og_text")
    @classmethod
    def validate_text(cls, v):
        """Validate text content."""
        return validate_text_content(v, min_length=10, max_length=500_000)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "client_uid": "user_123",
                "mode": "default",
                "og_text": "This is a sample text that needs to be summarized. It should be long enough to demonstrate the summarization capabilities of the system.",
            }
        }
    )


class SummarizeResponse(BaseModel):
    """Response model for text summarization with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: str | None = client_uid_field()
    summary: str | None = Field(None, description="Generated summary")
    error: str | None = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class WebSocketMessage(BaseModel):
    """Base WebSocket message with enhanced validation."""

    type: MessageType
    status: TaskStatus | None = None
    task_id: str | None = Field(
        None, min_length=1, max_length=100, description="Task identifier"
    )
    client_uid: str | None = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    error: str | None = Field(None, max_length=1000, description="Error message")

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class WebSocketSubtitleMessage(WebSocketMessage):
    """Subtitle-specific WebSocket message with enhanced validation."""

    title: str | None = Field(None, max_length=500, description="Video title")
    original_language: str | None = Field(None, description="ISO 639-1 language code")
    publish_date: str | None = Field(None, description="Video publish date")
    en_subtitles: str | None = Field(None, description="English subtitles")
    ru_subtitles: str | None = Field(None, description="Russian subtitles")

    @field_validator("original_language")
    @classmethod
    def validate_language(cls, v):
        """Validate language code format."""
        return validate_language_code(v)


class WebSocketSummarizeMessage(WebSocketMessage):
    """Summarize-specific WebSocket message with enhanced validation."""

    summary: str | None = Field(None, description="Generated summary")


class ConvertTTMLRequest(BaseModel):
    """Request model for TTML to TXT conversion with enhanced validation."""

    ttml_text: str = Field(
        ...,
        min_length=10,
        max_length=10_000_000,  # 10MB of text
        description="TTML content to convert to TXT format",
    )

    @field_validator("ttml_text")
    @classmethod
    def validate_ttml_content(cls, v):
        """Validate TTML content format."""
        if not v.strip():
            raise ValueError("TTML content cannot be empty")

        # Basic TTML format validation - should contain XML-like structure
        if not ("<tt" in v.lower() or "<p>" in v.lower() or "<?xml" in v.lower()):
            raise ValueError("Content does not appear to be valid TTML format")

        return v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "ttml_text": '<?xml version="1.0" encoding="utf-8"?><tt><body><div><p>Hello world</p></div></body></tt>'
            }
        }
    )


class ConvertTTMLResponse(BaseModel):
    """Response model for TTML to TXT conversion with enhanced validation."""

    status: TaskStatus
    txt_content: str | None = Field(None, description="Converted TXT content")
    error: str | None = Field(
        None, max_length=1000, description="Error message if conversion failed"
    )
    model_config = ConfigDict(
        json_schema_extra={
            "example": {"status": "completed", "txt_content": "Hello world"}
        }
    )


class WebSocketSummarizeRequest(BaseModel):
    """Request for summarization via WebSocket with enhanced validation."""

    type: MessageType = Field(
        ..., description="Message type (text_input or file_input)"
    )
    client_uid: str | None = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    mode: SummarizeMode | None = Field(None, description="Summarization mode")
    content: str = Field(
        ...,
        min_length=1,
        max_length=10_000_000,
        description="Text content or base64 encoded file content",
    )
    filename: str | None = Field(None, description="Filename (required for file_input)")

    @field_validator("client_uid")
    @classmethod
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    @field_validator("filename")
    @classmethod
    def validate_filename_format(cls, v):
        """Validate filename format and security."""
        return validate_filename(v)

    @field_validator("content")
    @classmethod
    def validate_content_by_type(cls, v, info: ValidationInfo):
        """Validate content based on message type."""
        message_type = info.data.get("type")

        if message_type == MessageType.TEXT:
            # For text input, validate as regular text
            return validate_text_content(v, min_length=10, max_length=500_000)
        elif message_type == MessageType.FILE:
            # For file input, content should be base64 encoded
            if not v:
                raise ValueError("File content cannot be empty")

            # Basic base64 validation (simplified)
            import base64

            try:
                # Try to decode to validate base64 format
                decoded = base64.b64decode(v, validate=True)
                if len(decoded) > ValidationConstants.MAX_FILE_SIZE:
                    raise ValueError("File size exceeds maximum allowed size")
            except Exception:
                raise ValueError("Invalid base64 encoded file content")

        return v

    @field_validator("filename")
    @classmethod
    def validate_filename_required_for_file(cls, v, info: ValidationInfo):
        """Ensure filename is provided for file input."""
        message_type = info.data.get("type")

        if message_type == MessageType.FILE and not v:
            raise ValueError("Filename is required for file input")

        return v
