import asyncio
import signal
import time
from collections import deque
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any

from fastapi import APIRouter
from loguru import logger
from prometheus_client import Counter, Gauge, Histogram, generate_latest

from models.schemas import HealthStatus, TaskStatus
from worker.error_handling import ErrorType
from worker.task_state import TaskState, TaskStateTracker

# Prometheus metrics
TASKS_STARTED = Counter(
    "tasks_started_total", "Total number of tasks started", ["task_type"]
)

TASKS_COMPLETED = Counter(
    "tasks_completed_total", "Total number of tasks completed", ["task_type", "status"]
)

TASKS_IN_PROGRESS = Gauge(
    "tasks_in_progress", "Number of tasks currently in progress", ["task_type"]
)

TASK_DURATION = Histogram(
    "task_duration_seconds",
    "Task duration in seconds",
    ["task_type"],
    buckets=(0.1, 0.25, 0.5, 1, 2.5, 5, 10, 30, 60, 120, 300, 600, 900, 1800, 3600),
)

TASK_QUEUE_LENGTH = Gauge(
    "task_queue_length", "Number of tasks in the queue", ["queue_name"]
)

TASK_RETRIES = Counter(
    "task_retries_total", "Total number of task retries", ["task_type"]
)

WATCHDOG_EVENTS = Counter(
    "watchdog_events_total", "Total number of watchdog events", ["event_type"]
)


@dataclass
class TaskRecoveryConfig:
    """Configuration for task recovery behavior."""

    max_retries: int = 3
    initial_backoff: float = 1.0  # seconds
    max_backoff: float = 60.0  # seconds
    backoff_multiplier: float = 2.0
    dead_letter_queue: bool = True
    dead_letter_ttl: int = 86400  # 24 hours in seconds


@dataclass
class DeadLetterTask:
    """Represents a task that has failed all retry attempts."""

    task_id: str
    task_type: str
    error: str
    error_type: ErrorType
    timestamp: datetime = field(default_factory=datetime.utcnow)
    retry_count: int = 0
    last_attempt: datetime | None = None
    task_data: dict[str, Any] | None = None
    traceback: str | None = None


class TaskWatchdog:
    """Monitors tasks for timeouts, handles task recovery, and provides health checks.

    The watchdog is responsible for:
    - Detecting and handling stalled tasks
    - Managing task retries with backoff
    - Maintaining a dead-letter queue for failed tasks
    - Providing health check endpoints
    - Collecting metrics and monitoring
    """

    def __init__(
        self,
        task_tracker: TaskStateTracker,
        check_interval: float = 30.0,
        recovery_config: TaskRecoveryConfig | None = None,
    ):
        """Initialize the TaskWatchdog.

        Args:
            task_tracker: The task tracker to monitor
            check_interval: How often to check for stalled tasks (in seconds)
            recovery_config: Configuration for task recovery behavior
        """
        self.task_tracker = task_tracker
        self.check_interval = check_interval
        self.recovery_config = recovery_config or TaskRecoveryConfig()

        # Task management
        self.watchdog_task: asyncio.Task | None = None
        self.running = False
        self._shutdown_event = asyncio.Event()
        self._shutdown_timeout = 30.0  # seconds to wait for graceful shutdown

        # Handlers and callbacks
        self.stall_handlers: dict[str, Callable[[str, TaskState], None]] = {}
        self.recovery_handlers: dict[str, Callable[[str, TaskState], bool]] = {}

        # Dead letter queue
        self.dead_letter_queue: deque[DeadLetterTask] = deque(maxlen=1000)
        self._dead_letter_lock = asyncio.Lock()

        # Health status
        self._health_status = HealthStatus.HEALTHY
        self._health_errors: list[str] = []
        self._start_time = datetime.utcnow()

        # Metrics
        self._last_check_time = time.monotonic()
        self._task_retries: dict[str, int] = {}

        # Setup FastAPI router for health checks
        self.router = APIRouter()
        self._setup_routes()

        # Register signal handlers for graceful shutdown
        self._setup_signal_handlers()

    def _setup_routes(self) -> None:
        """Set up FastAPI routes for health checks and metrics."""
        self.router.add_api_route("/health", self.health_check, methods=["GET"])
        self.router.add_api_route("/metrics", self.metrics_endpoint, methods=["GET"])

    def _setup_signal_handlers(self) -> None:
        """Set up signal handlers for graceful shutdown."""
        loop = asyncio.get_event_loop()
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(
                sig, lambda s=sig: asyncio.create_task(self._handle_shutdown_signal(s))
            )

    async def _handle_shutdown_signal(self, sig: signal.Signals) -> None:
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {sig.name}, initiating graceful shutdown...")
        await self.stop()

    def register_stall_handler(
        self, task_type: str, handler: Callable[[str, TaskState], None]
    ) -> None:
        """Register a handler for stalled tasks of a specific type.

        Args:
            task_type: The type of task to handle
            handler: A function that takes (task_id: str, task_state: TaskState) and returns None
        """
        self.stall_handlers[task_type] = handler
        logger.info(f"Registered stall handler for task type: {task_type}")

    async def start(self) -> None:
        """Start the watchdog monitoring.

        Raises:
            RuntimeError: If the watchdog is already running
        """
        if self.running:
            raise RuntimeError("TaskWatchdog is already running")

        logger.info("Starting TaskWatchdog...")
        self.running = True
        self._shutdown_event.clear()
        self.watchdog_task = asyncio.create_task(self._monitor_tasks())
        logger.info(f"TaskWatchdog started with check interval {self.check_interval}s")

    async def stop(self, timeout: float | None = None) -> None:
        """Stop the watchdog monitoring gracefully.

        Args:
            timeout: Maximum time to wait for graceful shutdown (in seconds).
                    If None, uses the default shutdown timeout.
        """
        if not self.running:
            logger.debug("TaskWatchdog not running")
            return

        logger.info("Stopping TaskWatchdog...")
        self.running = False
        self._shutdown_event.set()

        # Wait for the monitoring task to complete
        if self.watchdog_task and not self.watchdog_task.done():
            timeout = timeout or self._shutdown_timeout
            try:
                await asyncio.wait_for(self.watchdog_task, timeout=timeout)
            except TimeoutError:
                logger.warning(
                    "TaskWatchdog did not stop within timeout, forcing cancellation"
                )
                self.watchdog_task.cancel()
                try:
                    await self.watchdog_task
                except (asyncio.CancelledError, Exception) as e:
                    logger.warning(f"Error during watchdog task cancellation: {str(e)}")

        logger.info("TaskWatchdog stopped")

    async def _monitor_tasks(self) -> None:
        """Main monitoring loop that checks for stalled tasks and handles recovery."""
        logger.info("TaskWatchdog monitoring loop started")

        while self.running:
            try:
                start_time = time.monotonic()

                # Check for stalled tasks
                stalled_tasks = self.task_tracker.check_stalled_tasks()
                if stalled_tasks:
                    logger.warning(f"Detected {len(stalled_tasks)} stalled tasks")
                    WATCHDOG_EVENTS.labels(event_type="stall_detected").inc(
                        len(stalled_tasks)
                    )

                # Handle each stalled task
                for task_id in stalled_tasks:
                    task_state = self.task_tracker.get_task_state(task_id)
                    if not task_state:
                        continue

                    # Try to recover the task if possible
                    recovered = await self._handle_stalled_task(task_id, task_state)
                    if not recovered:
                        # If recovery failed, log and update metrics
                        logger.error(f"Failed to recover stalled task {task_id}")
                        WATCHDOG_EVENTS.labels(event_type="recovery_failed").inc()

                # Update metrics
                self._update_metrics()

                # Check dead letter queue TTL
                await self._cleanup_dead_letter_queue()

                # Calculate time to sleep (account for processing time)
                processing_time = time.monotonic() - start_time
                sleep_time = max(0.0, self.check_interval - processing_time)

                # Wait until next check or shutdown signal
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(), timeout=sleep_time
                    )
                    if self._shutdown_event.is_set():
                        break
                except TimeoutError:
                    # Normal case - continue to next iteration
                    pass

            except Exception as e:
                logger.error(
                    f"Unexpected error in TaskWatchdog monitoring loop: {str(e)}",
                    exc_info=True,
                )
                WATCHDOG_EVENTS.labels(event_type="monitor_error").inc()
                # Short delay before retrying after an error
                await asyncio.sleep(5.0)

        logger.info("TaskWatchdog monitoring loop stopped")

    async def _handle_stalled_task(self, task_id: str, task_state: TaskState) -> bool:
        """Handle a stalled task by attempting recovery or moving to dead letter queue.

        Returns:
            bool: True if the task was recovered, False otherwise
        """
        task_type = task_state.task_type

        # Check if we should retry the task
        should_retry = await self._should_retry_task(task_id, task_state)

        if should_retry:
            # Get the recovery handler for this task type
            recovery_handler = self.recovery_handlers.get(task_type)
            if recovery_handler:
                try:
                    # Try to recover the task
                    success = await asyncio.get_event_loop().run_in_executor(
                        None,  # Use default executor
                        lambda: recovery_handler(task_id, task_state),
                    )

                    if success:
                        logger.info(
                            f"Successfully recovered task {task_id} (type: {task_type})"
                        )
                        WATCHDOG_EVENTS.labels(event_type="recovery_success").inc()
                        return True

                except Exception as e:
                    logger.error(
                        f"Error in recovery handler for task {task_id}: {str(e)}",
                        exc_info=True,
                    )

        # If we get here, recovery failed or wasn't attempted
        await self._move_to_dead_letter_queue(
            task_id, task_state, "Task stalled and recovery failed"
        )
        return False

    async def _should_retry_task(self, task_id: str, task_state: TaskState) -> bool:
        """Determine if a task should be retried based on its retry count and configuration."""
        if not self.recovery_config or self.recovery_config.max_retries <= 0:
            return False

        retry_count = self._task_retries.get(task_id, 0)
        if retry_count >= self.recovery_config.max_retries:
            logger.warning(
                f"Task {task_id} exceeded max retries ({self.recovery_config.max_retries})"
            )
            return False

        # Calculate backoff time
        backoff = min(
            self.recovery_config.initial_backoff
            * (self.recovery_config.backoff_multiplier**retry_count),
            self.recovery_config.max_backoff,
        )

        # Update retry count and schedule retry
        self._task_retries[task_id] = retry_count + 1
        TASK_RETRIES.labels(task_type=task_state.task_type).inc()

        logger.info(
            f"Scheduling retry {retry_count + 1} for task {task_id} in {backoff:.2f}s"
        )

        # Schedule the retry
        asyncio.create_task(self._schedule_retry(task_id, backoff))
        return True

    async def _schedule_retry(self, task_id: str, delay: float) -> None:
        """Schedule a task to be retried after a delay."""
        try:
            await asyncio.sleep(delay)

            # Check if the task still exists and is still in a retryable state
            task_state = self.task_tracker.get_task_state(task_id)
            if not task_state or not task_state.is_active:
                logger.warning(f"Task {task_id} no longer active, skipping retry")
                return

            # Reset the task state to PENDING so it can be picked up again
            task_state.status = TaskStatus.PENDING
            task_state.progress = 0.0
            task_state.error_details = None

            logger.info(f"Retrying task {task_id}")

        except Exception as e:
            logger.error(f"Error in task retry for {task_id}: {str(e)}", exc_info=True)

    async def _move_to_dead_letter_queue(
        self, task_id: str, task_state: TaskState, error_message: str
    ) -> None:
        """Move a failed task to the dead letter queue."""
        if not self.recovery_config.dead_letter_queue:
            return

        async with self._dead_letter_lock:
            dead_task = DeadLetterTask(
                task_id=task_id,
                task_type=task_state.task_type,
                error=error_message,
                error_type=ErrorType.PROCESSING,
                retry_count=self._task_retries.get(task_id, 0),
                last_attempt=datetime.utcnow(),
                task_data=task_state.to_dict(),
            )

            self.dead_letter_queue.append(dead_task)
            WATCHDOG_EVENTS.labels(event_type="dead_letter").inc()

            logger.warning(
                f"Moved task {task_id} to dead letter queue. Reason: {error_message}"
            )

    async def _cleanup_dead_letter_queue(self) -> None:
        """Remove expired tasks from the dead letter queue."""
        if not self.recovery_config.dead_letter_queue:
            return

        now = datetime.utcnow()
        ttl = timedelta(seconds=self.recovery_config.dead_letter_ttl)

        async with self._dead_letter_lock:
            # Remove tasks older than TTL
            initial_count = len(self.dead_letter_queue)
            self.dead_letter_queue = deque(
                t for t in self.dead_letter_queue if now - t.timestamp < ttl
            )

            removed = initial_count - len(self.dead_letter_queue)
            if removed > 0:
                logger.info(
                    f"Cleaned up {removed} expired tasks from dead letter queue"
                )

    def _update_metrics(self) -> None:
        """Update Prometheus metrics."""
        # Update queue length metrics
        if hasattr(self.task_tracker, "get_queue_lengths"):
            try:
                queue_lengths = self.task_tracker.get_queue_lengths()
                for queue_name, length in queue_lengths.items():
                    TASK_QUEUE_LENGTH.labels(queue_name=queue_name).set(length)
            except Exception as e:
                logger.error(f"Error updating queue metrics: {str(e)}", exc_info=True)

        # Update in-progress tasks count by type
        if hasattr(self.task_tracker, "get_active_tasks"):
            try:
                active_tasks = self.task_tracker.get_active_tasks()
                task_type_counts: dict[str, int] = {}

                for task_id in active_tasks:
                    task_state = self.task_tracker.get_task_state(task_id)
                    if task_state and task_state.status == TaskStatus.PROCESSING:
                        task_type = task_state.task_type
                        task_type_counts[task_type] = (
                            task_type_counts.get(task_type, 0) + 1
                        )

                # Update metrics for each task type
                for task_type, count in task_type_counts.items():
                    TASKS_IN_PROGRESS.labels(task_type=task_type).set(count)

            except Exception as e:
                logger.error(f"Error updating task metrics: {str(e)}", exc_info=True)

    # Health check endpoints
    async def health_check(self) -> dict[str, Any]:
        """Health check endpoint that returns the current service status."""
        checks: list[dict[str, Any]] = []
        is_healthy = True

        # Check task tracker
        try:
            if not self.task_tracker:
                raise RuntimeError("Task tracker not initialized")

            # Add more specific health checks here
            checks.append(
                {
                    "name": "task_tracker",
                    "status": "healthy",
                    "details": {
                        "task_count": (
                            len(self.task_tracker.tasks)
                            if hasattr(self.task_tracker, "tasks")
                            else 0
                        )
                    },
                }
            )
        except Exception as e:
            is_healthy = False
            checks.append(
                {"name": "task_tracker", "status": "unhealthy", "error": str(e)}
            )

        # Check dead letter queue
        try:
            dlq_size = len(self.dead_letter_queue)
            checks.append(
                {
                    "name": "dead_letter_queue",
                    "status": "healthy" if dlq_size == 0 else "warning",
                    "details": {"size": dlq_size},
                }
            )

            if dlq_size > 50:  # Warning threshold
                is_healthy = False

        except Exception as e:
            is_healthy = False
            checks.append(
                {"name": "dead_letter_queue", "status": "unhealthy", "error": str(e)}
            )

        # Update global health status
        self._health_status = (
            HealthStatus.HEALTHY if is_healthy else HealthStatus.UNHEALTHY
        )

        return {
            "status": self._health_status.value,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": (datetime.utcnow() - self._start_time).total_seconds(),
            "checks": checks,
        }

    async def metrics_endpoint(self) -> bytes:
        """Prometheus metrics endpoint."""
        return generate_latest()

    # Public API
    def register_stall_handler(
        self, task_type: str, handler: Callable[[str, TaskState], None]
    ) -> None:
        """Register a handler for stalled tasks of a specific type."""
        self.stall_handlers[task_type] = handler
        logger.info(f"Registered stall handler for task type: {task_type}")

    def register_recovery_handler(
        self, task_type: str, handler: Callable[[str, TaskState], bool]
    ) -> None:
        """Register a recovery handler for tasks of a specific type."""
        self.recovery_handlers[task_type] = handler
        logger.info(f"Registered recovery handler for task type: {task_type}")

    async def handle_stalled_task(self, task_id: str, action: str = "cancel"):
        """Обрабатывает зависшую задачу"""
        task_state = self.task_tracker.get_task_state(task_id)
        if not task_state:
            logger.warning(f"Cannot handle stalled task {task_id}: task not found")
            return

        if action == "cancel":
            # Отмечаем задачу как отмененную в трекере
            self.task_tracker.cancel_task(
                task_id, "Task cancelled due to stall detection"
            )
            logger.info(
                f"Stalled task {task_id} of type {task_state.task_type} marked as cancelled"
            )
            return True
        elif action == "retry":
            # Логика для повторной попытки выполнения задачи
            # Это должно быть реализовано в конкретном обработчике
            logger.info(
                f"Retry requested for stalled task {task_id} of type {task_state.task_type}"
            )
            return True
        else:
            logger.warning(f"Unknown action '{action}' for stalled task {task_id}")
            return False

    def get_monitoring_stats(self) -> dict[str, Any]:
        """Возвращает статистику мониторинга"""
        return {
            "running": self.running,
            "check_interval": self.check_interval,
            "stalled_tasks": self.task_tracker.get_stalled_tasks_info(),
            "active_tasks": {
                "count": len(self.task_tracker.active_tasks),
                "by_type": self.task_tracker.get_active_tasks_count(),
            },
        }
