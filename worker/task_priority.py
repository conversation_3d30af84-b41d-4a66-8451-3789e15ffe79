from dataclasses import dataclass, field
from datetime import datetime
from enum import IntEnum
from typing import Any


class TaskPriority(IntEnum):
    """Priority levels for tasks.

    Higher values indicate higher priority.
    """

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass(order=True)
class PrioritizedTask:
    """A task with priority for use in priority queues."""

    priority: TaskPriority
    data: Any = field(compare=False)
    task_id: str = field(compare=False)
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())


def get_priority_from_string(priority_str: str | None) -> TaskPriority:
    """Convert string priority to TaskPriority enum.

    Args:
        priority_str: String representation of priority (case-insensitive)

    Returns:
        TaskPriority: Corresponding enum value, defaults to NORMAL
    """
    if not priority_str:
        return TaskPriority.NORMAL

    priority_map = {
        "low": TaskPriority.LOW,
        "normal": TaskPriority.NORMAL,
        "high": TaskPriority.HIGH,
        "urgent": TaskPriority.URGENT,
    }
    return priority_map.get(priority_str.lower(), TaskPriority.NORMAL)
