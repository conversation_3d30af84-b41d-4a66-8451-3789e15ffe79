import asyncio
import time
from collections import deque
from dataclasses import dataclass
from enum import Enum

import structlog
from loguru import logger as loguru_logger

# Use structured logger if available, fall back to loguru
try:
    logger = structlog.get_logger(__name__)
except Exception:
    logger = loguru_logger


class ServiceType(str, Enum):
    """Types of external services that need rate limiting."""

    YOUTUBE = "youtube"
    GEMINI = "gemini"
    OPENAI = "openai"


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""

    requests_per_minute: int
    requests_per_hour: int
    burst_limit: int = 5
    cooldown_seconds: int = 1
    max_retries: int = 3


class AdvancedRateLimiter:
    """
    Advanced rate limiter with sliding window, burst protection, and adaptive limits.

    Features:
    - Sliding window rate limiting
    - Burst protection
    - Adaptive rate limiting based on service response
    - Retry logic with exponential backoff
    - Per-service rate limiting
    """

    def __init__(self, config: RateLimitConfig, service_type: ServiceType):
        """Initialize the rate limiter.

        Args:
            config: Rate limiting configuration
            service_type: Type of service being rate limited
        """
        self.config = config
        self.service_type = service_type

        # Sliding window tracking
        self.request_times: deque = deque()
        self.task_timestamps: dict[str, float] = {}

        # Adaptive rate limiting
        self.consecutive_failures = 0
        self.last_failure_time = 0.0
        self.adaptive_multiplier = 1.0

        # Burst protection
        self.burst_requests: deque = deque()

        # Statistics
        self.total_requests = 0
        self.total_blocked = 0
        self.total_retries = 0

    async def can_make_request(self, task_id: str | None = None) -> tuple[bool, float]:
        """
        Check if a request can be made according to rate limits.

        Args:
            task_id: Optional task identifier

        Returns:
            Tuple of (can_make_request, wait_time_seconds)
        """
        current_time = time.time()
        self._cleanup_old_requests(current_time)

        # Check if we're in a cooldown period due to failures
        if self._is_in_cooldown(current_time):
            cooldown_remaining = self._get_cooldown_remaining(current_time)
            logger.debug(
                "Request blocked due to cooldown",
                service=self.service_type,
                cooldown_remaining=cooldown_remaining,
                consecutive_failures=self.consecutive_failures,
            )
            return False, cooldown_remaining

        # Apply adaptive rate limiting
        effective_limit_per_minute = int(
            self.config.requests_per_minute / self.adaptive_multiplier
        )
        effective_limit_per_hour = int(
            self.config.requests_per_hour / self.adaptive_multiplier
        )

        # Check minute window
        minute_ago = current_time - 60
        requests_last_minute = sum(
            1 for req_time in self.request_times if req_time > minute_ago
        )

        # Check hour window
        hour_ago = current_time - 3600
        requests_last_hour = sum(
            1 for req_time in self.request_times if req_time > hour_ago
        )

        # Check burst protection (last 10 seconds)
        burst_window = current_time - 10
        burst_requests = sum(
            1 for req_time in self.burst_requests if req_time > burst_window
        )

        # Determine if request is allowed
        if (
            requests_last_minute >= effective_limit_per_minute
            or requests_last_hour >= effective_limit_per_hour
            or burst_requests >= self.config.burst_limit
        ):
            # Calculate wait time
            wait_time = self._calculate_wait_time(
                current_time, requests_last_minute, effective_limit_per_minute
            )

            self.total_blocked += 1
            logger.debug(
                "Request blocked by rate limit",
                service=self.service_type,
                requests_last_minute=requests_last_minute,
                effective_limit_per_minute=effective_limit_per_minute,
                requests_last_hour=requests_last_hour,
                effective_limit_per_hour=effective_limit_per_hour,
                burst_requests=burst_requests,
                burst_limit=self.config.burst_limit,
                wait_time=wait_time,
            )

            return False, wait_time

        return True, 0.0

    async def record_request(
        self, task_id: str | None = None, success: bool = True
    ) -> None:
        """
        Record a request and its outcome.

        Args:
            task_id: Optional task identifier
            success: Whether the request was successful
        """
        current_time = time.time()

        # Record the request
        self.request_times.append(current_time)
        self.burst_requests.append(current_time)
        self.total_requests += 1

        if task_id:
            self.task_timestamps[task_id] = current_time

        # Update adaptive rate limiting based on success/failure
        if success:
            self._record_success()
        else:
            self._record_failure(current_time)

        logger.debug(
            "Request recorded",
            service=self.service_type,
            task_id=task_id,
            success=success,
            adaptive_multiplier=self.adaptive_multiplier,
            consecutive_failures=self.consecutive_failures,
        )

    async def wait_if_needed(self, task_id: str | None = None) -> bool:
        """
        Wait if rate limiting requires it, with retry logic.

        Args:
            task_id: Optional task identifier

        Returns:
            True if request can proceed, False if max retries exceeded
        """
        retries = 0

        while retries < self.config.max_retries:
            can_proceed, wait_time = await self.can_make_request(task_id)

            if can_proceed:
                return True

            if wait_time > 0:
                # Exponential backoff with jitter
                backoff_time = min(wait_time * (2**retries), 60)  # Max 60 seconds
                jitter = backoff_time * 0.1 * (0.5 - time.time() % 1)  # ±10% jitter
                actual_wait = backoff_time + jitter

                logger.info(
                    "Rate limit hit, waiting before retry",
                    service=self.service_type,
                    task_id=task_id,
                    retry=retries + 1,
                    max_retries=self.config.max_retries,
                    wait_time=actual_wait,
                )

                await asyncio.sleep(actual_wait)
                retries += 1
                self.total_retries += 1
            else:
                break

        logger.warning(
            "Max retries exceeded for rate limiting",
            service=self.service_type,
            task_id=task_id,
            max_retries=self.config.max_retries,
        )
        return False

    def remove_task(self, task_id: str) -> None:
        """Remove a task from tracking."""
        if task_id in self.task_timestamps:
            del self.task_timestamps[task_id]
            logger.debug(
                "Task removed from rate limiter",
                service=self.service_type,
                task_id=task_id,
            )

    def get_stats(self) -> dict[str, any]:
        """Get rate limiter statistics."""
        current_time = time.time()
        self._cleanup_old_requests(current_time)

        minute_ago = current_time - 60
        hour_ago = current_time - 3600

        return {
            "service_type": self.service_type,
            "total_requests": self.total_requests,
            "total_blocked": self.total_blocked,
            "total_retries": self.total_retries,
            "requests_last_minute": sum(
                1 for req_time in self.request_times if req_time > minute_ago
            ),
            "requests_last_hour": sum(
                1 for req_time in self.request_times if req_time > hour_ago
            ),
            "adaptive_multiplier": self.adaptive_multiplier,
            "consecutive_failures": self.consecutive_failures,
            "active_tasks": len(self.task_timestamps),
            "config": {
                "requests_per_minute": self.config.requests_per_minute,
                "requests_per_hour": self.config.requests_per_hour,
                "burst_limit": self.config.burst_limit,
                "cooldown_seconds": self.config.cooldown_seconds,
                "max_retries": self.config.max_retries,
            },
        }

    def _cleanup_old_requests(self, current_time: float) -> None:
        """Clean up old request timestamps."""
        # Clean up request times (keep last hour)
        hour_ago = current_time - 3600
        while self.request_times and self.request_times[0] < hour_ago:
            self.request_times.popleft()

        # Clean up burst requests (keep last 10 seconds)
        burst_window = current_time - 10
        while self.burst_requests and self.burst_requests[0] < burst_window:
            self.burst_requests.popleft()

        # Clean up old task timestamps
        for task_id, timestamp in list(self.task_timestamps.items()):
            if timestamp < hour_ago:
                del self.task_timestamps[task_id]

    def _is_in_cooldown(self, current_time: float) -> bool:
        """Check if we're in a cooldown period due to failures."""
        if self.consecutive_failures == 0:
            return False

        cooldown_duration = self.config.cooldown_seconds * (
            2 ** min(self.consecutive_failures - 1, 5)
        )
        return current_time - self.last_failure_time < cooldown_duration

    def _get_cooldown_remaining(self, current_time: float) -> float:
        """Get remaining cooldown time."""
        cooldown_duration = self.config.cooldown_seconds * (
            2 ** min(self.consecutive_failures - 1, 5)
        )
        return max(0, cooldown_duration - (current_time - self.last_failure_time))

    def _calculate_wait_time(
        self, current_time: float, requests_count: int, limit: int
    ) -> float:
        """Calculate how long to wait before next request."""
        if not self.request_times:
            return 1.0

        # Find the oldest request in the current window
        minute_ago = current_time - 60
        recent_requests = [
            req_time for req_time in self.request_times if req_time > minute_ago
        ]

        if len(recent_requests) < limit:
            return 0.0

        # Wait until the oldest request falls out of the window
        oldest_request = min(recent_requests)
        return max(1.0, oldest_request + 60 - current_time)

    def _record_success(self) -> None:
        """Record a successful request."""
        if self.consecutive_failures > 0:
            self.consecutive_failures = 0
            # Gradually restore normal rate limiting
            self.adaptive_multiplier = max(1.0, self.adaptive_multiplier * 0.9)

    def _record_failure(self, current_time: float) -> None:
        """Record a failed request."""
        self.consecutive_failures += 1
        self.last_failure_time = current_time

        # Increase rate limiting severity
        self.adaptive_multiplier = min(4.0, self.adaptive_multiplier * 1.5)


# Backward compatibility classes
class YouTubeRateLimiter(AdvancedRateLimiter):
    """Rate limiter for YouTube API requests."""

    def __init__(self, limit_per_minute: int = 15):
        config = RateLimitConfig(
            requests_per_minute=limit_per_minute,
            requests_per_hour=limit_per_minute * 4,  # 4x per hour
            burst_limit=3,
            cooldown_seconds=2,
            max_retries=3,
        )
        super().__init__(config, ServiceType.YOUTUBE)

    # Legacy methods for backward compatibility
    def can_make_request(self) -> bool:
        """Legacy sync method - use async version instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            can_proceed, _ = loop.run_until_complete(super().can_make_request())
            return can_proceed
        except:
            return True  # Fail open for compatibility

    def add_request(self, task_id: str | None = None) -> None:
        """Legacy method - use record_request instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(super().record_request(task_id, success=True))
        except:
            pass  # Fail silently for compatibility

    def get_wait_time(self) -> float:
        """Legacy method - use can_make_request instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            can_proceed, wait_time = loop.run_until_complete(super().can_make_request())
            return wait_time if not can_proceed else 0.0
        except:
            return 0.0  # Fail open for compatibility


class SummarizeRateLimiter(AdvancedRateLimiter):
    """Rate limiter for summarization API requests."""

    def __init__(self, limit_per_minute: int = 5):
        config = RateLimitConfig(
            requests_per_minute=limit_per_minute,
            requests_per_hour=limit_per_minute * 6,  # 6x per hour
            burst_limit=2,
            cooldown_seconds=3,
            max_retries=3,
        )
        super().__init__(config, ServiceType.GEMINI)

    # Legacy methods for backward compatibility
    def can_make_request(self) -> bool:
        """Legacy sync method - use async version instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            can_proceed, _ = loop.run_until_complete(super().can_make_request())
            return can_proceed
        except:
            return True  # Fail open for compatibility

    def add_request(self, task_id: str | None = None) -> None:
        """Legacy method - use record_request instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(super().record_request(task_id, success=True))
        except:
            pass  # Fail silently for compatibility

    def get_wait_time(self) -> float:
        """Legacy method - use can_make_request instead."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            can_proceed, wait_time = loop.run_until_complete(super().can_make_request())
            return wait_time if not can_proceed else 0.0
        except:
            return 0.0  # Fail open for compatibility


# Factory functions for creating rate limiters with configuration
def create_youtube_rate_limiter(
    requests_per_minute: int = 15, requests_per_hour: int = 60, burst_limit: int = 3
) -> AdvancedRateLimiter:
    """Create a YouTube rate limiter with custom configuration."""
    config = RateLimitConfig(
        requests_per_minute=requests_per_minute,
        requests_per_hour=requests_per_hour,
        burst_limit=burst_limit,
        cooldown_seconds=2,
        max_retries=3,
    )
    return AdvancedRateLimiter(config, ServiceType.YOUTUBE)


def create_gemini_rate_limiter(
    requests_per_minute: int = 5, requests_per_hour: int = 30, burst_limit: int = 2
) -> AdvancedRateLimiter:
    """Create a Gemini AI rate limiter with custom configuration."""
    config = RateLimitConfig(
        requests_per_minute=requests_per_minute,
        requests_per_hour=requests_per_hour,
        burst_limit=burst_limit,
        cooldown_seconds=3,
        max_retries=3,
    )
    return AdvancedRateLimiter(config, ServiceType.GEMINI)


def create_openai_rate_limiter(
    requests_per_minute: int = 10, requests_per_hour: int = 100, burst_limit: int = 3
) -> AdvancedRateLimiter:
    """Create an OpenAI rate limiter with custom configuration."""
    config = RateLimitConfig(
        requests_per_minute=requests_per_minute,
        requests_per_hour=requests_per_hour,
        burst_limit=burst_limit,
        cooldown_seconds=2,
        max_retries=3,
    )
    return AdvancedRateLimiter(config, ServiceType.OPENAI)
