from __future__ import annotations

import asyncio
import time
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from typing import (
    Any,
    Generic,
    TypeVar,
)
from uuid import uuid4

from loguru import logger

from models.schemas import TaskStatus
from worker.error_handling import ErrorDetails, ErrorType

# Type variable for task result type
T = TypeVar("T")


@dataclass
class TaskProgress:
    """Represents a point-in-time progress update for a task."""

    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    progress: float  # 0.0 to 1.0
    message: str | None = None
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def progress_percent(self) -> float:
        """Get progress as a percentage."""
        return self.progress * 100


class TaskState(Generic[T]):
    """Tracks the state and progress of an asynchronous task.

    This class provides a thread-safe way to track task state, progress, and results.
    It supports task lifecycle events, progress tracking, and error handling.

    Attributes:
        task_id: Unique identifier for the task
        task_type: Type/category of the task (e.g., 'subtitle', 'summarize')
        status: Current status of the task
        progress: Current progress (0.0 to 1.0)
        created_at: When the task was created
        started_at: When the task started processing
        completed_at: When the task completed
        expected_duration: Estimated duration in seconds (for progress estimation)
        error_details: Structured error information if the task failed
        result: The result of the task if completed successfully
    """

    def __init__(
        self,
        task_id: str | None = None,
        task_type: str = "default",
        expected_duration: float | None = None,
        metadata: dict[str, Any] | None = None,
        timeout_seconds: float | None = None,
    ):
        """Initialize a new TaskState.

        Args:
            task_id: Optional unique ID for the task (auto-generated if not provided)
            task_type: Type/category of the task
            expected_duration: Estimated duration in seconds
            metadata: Additional task metadata
            timeout_seconds: Optional timeout in seconds after which the task will be considered failed
        """
        self._task_id = task_id or f"task_{uuid4().hex[:8]}"
        self.task_type = task_type
        self.status = TaskStatus.PENDING
        self.progress = 0.0
        self.created_at = datetime.now(UTC)
        self.started_at: datetime | None = None
        self.completed_at: datetime | None = None
        self.expected_duration = expected_duration
        self.timeout_seconds = timeout_seconds
        self._progress_history: list[TaskProgress] = []
        self.error_details: ErrorDetails | None = None
        self._result: T | None = None
        self.metadata = metadata or {}
        self._callbacks: dict[str, list[Callable[[TaskState], None]]] = {
            "progress": [],
            "complete": [],
            "error": [],
            "status_change": [],
            "timeout": [],
        }
        self._timeout_handle: asyncio.TimerHandle | None = None
        self._lock = asyncio.Lock()

        # Set up timeout if specified
        if self.timeout_seconds is not None and self.timeout_seconds > 0:
            self._setup_timeout()

    def _setup_timeout(self) -> None:
        """Set up the task timeout if specified."""
        if self.timeout_seconds and self.status == TaskStatus.PROCESSING:
            loop = asyncio.get_event_loop()
            self._timeout_handle = loop.call_later(
                self.timeout_seconds,
                lambda: asyncio.create_task(self._handle_timeout()),
            )

    async def _handle_timeout(self) -> None:
        """Handle task timeout by marking it as failed."""
        if self.status == TaskStatus.PROCESSING:
            await self.fail(
                f"Task timed out after {self.timeout_seconds} seconds",
                error_type=ErrorType.TIMEOUT,
                retryable=True,
            )
            # Trigger timeout callbacks
            await self._trigger_callbacks("timeout")

    def _clear_timeout(self) -> None:
        """Clear any pending timeout."""
        if self._timeout_handle is not None:
            self._timeout_handle.cancel()
            self._timeout_handle = None

    @property
    def task_id(self) -> str:
        """Get the task's unique identifier."""
        return self._task_id

    @property
    def is_active(self) -> bool:
        """Check if the task is currently active (PENDING or PROCESSING)."""
        return self.status in (TaskStatus.PENDING, TaskStatus.PROCESSING)

    @property
    def is_completed(self) -> bool:
        """Check if the task has completed successfully."""
        return self.status == TaskStatus.COMPLETED

    @property
    def has_failed(self) -> bool:
        """Check if the task has failed."""
        return self.status == TaskStatus.FAILED

    @property
    def is_cancelled(self) -> bool:
        """Check if the task was cancelled."""
        return self.status == TaskStatus.CANCELLED

    @property
    def duration(self) -> float | None:
        """Get the duration of the task in seconds if completed, None otherwise."""
        if self.completed_at and self.started_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    @property
    def result(self) -> T | None:
        """Get the task result if available."""
        return self._result

    @result.setter
    def result(self, value: T) -> None:
        """Set the task result and mark as completed if not already done.

        Args:
            value: The result value to set

        Raises:
            RuntimeError: If the task is not in a state that can accept a result
        """
        if self.status not in (TaskStatus.PROCESSING, TaskStatus.PENDING):
            raise RuntimeError(
                f"Cannot set result for task in state {self.status}. "
                "Task must be in PROCESSING or PENDING state."
            )
        self._result = value
        if self.status != TaskStatus.COMPLETED:
            self.mark_completed()

    @property
    def is_completed(self) -> bool:
        """Check if the task has completed successfully."""
        return self.status == TaskStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """Check if the task has failed."""
        return self.status == TaskStatus.FAILED

    @property
    def is_cancelled(self) -> bool:
        """Check if the task was cancelled."""
        return self.status == TaskStatus.CANCELLED

    @property
    def is_processing(self) -> bool:
        """Check if the task is currently processing."""
        return self.status == TaskStatus.PROCESSING

    @property
    def is_pending(self) -> bool:
        """Check if the task is pending execution."""
        return self.status == TaskStatus.PENDING

    def mark_started(self, expected_duration: float | None = None, **metadata) -> None:
        """Mark the task as started.

        Args:
            expected_duration: Optional override for expected duration
            **metadata: Additional metadata to include with the task
        """
        if self.status != TaskStatus.PENDING:
            raise ValueError(f"Cannot start task in {self.status} state")

        self.started_at = datetime.now(UTC)
        self.status = TaskStatus.PROCESSING

        if expected_duration is not None:
            self.expected_duration = expected_duration

        self.metadata.update(metadata)
        self._record_progress(0.0, "Task started")
        self._trigger_callbacks("status_change")

    def update_progress(
        self, progress: float, message: str | None = None, **metadata
    ) -> None:
        """Update the task's progress.

        Args:
            progress: Progress value between 0.0 and 1.0
            message: Optional progress message
            **metadata: Additional metadata to include with this progress update
        """
        if self.status != TaskStatus.PROCESSING:
            raise ValueError(f"Cannot update progress for task in {self.status} state")

        progress = max(0.0, min(1.0, progress))
        self.progress = progress

        # Only record significant progress updates (every 5% or if message is provided)
        if (progress % 0.05 < 0.01 or message) and progress < 1.0:
            self._record_progress(progress, message, **metadata)

        # Always update last progress for time-based checks
        self._progress_history[-1] = TaskProgress(
            timestamp=datetime.now(UTC),
            progress=progress,
            message=message,
            metadata=metadata,
        )

        self._trigger_callbacks("progress")

        # Log significant progress updates
        if message or (progress > 0 and progress % 0.1 < 0.01):
            logger.debug(
                f"Task {self.task_id} progress: {progress:.1%}"
                + (f" - {message}" if message else ""),
                task_id=self.task_id,
                progress=progress,
                **metadata,
            )

    def _record_progress(
        self, progress: float, message: str | None = None, **metadata
    ) -> None:
        """Record a progress point in the task's history."""
        self._progress_history.append(
            TaskProgress(progress=progress, message=message, metadata=metadata)
        )

    def mark_completed(
        self, result: T | None = None, message: str | None = None, **metadata
    ) -> None:
        """Mark the task as successfully completed.

        Args:
            result: Optional result of the task
            message: Optional completion message
            **metadata: Additional metadata to include
        """
        if self.status != TaskStatus.PROCESSING:
            raise ValueError(f"Cannot complete task in {self.status} state")

        self.completed_at = datetime.now(UTC)
        self.status = TaskStatus.COMPLETED
        self._result = result
        self.progress = 1.0

        if message or metadata:
            self._record_progress(
                1.0, message or "Task completed successfully", **metadata
            )

        logger.info(
            f"Task {self.task_id} completed successfully in {self.get_execution_time():.2f}s"
            + (f" - {message}" if message else ""),
            task_id=self.task_id,
            task_type=self.task_type,
            duration=self.get_execution_time(),
        )

        self._trigger_callbacks("complete")
        self._trigger_callbacks("status_change")

    def mark_failed(
        self,
        error: str | Exception | ErrorDetails,
        error_type: ErrorType | None = None,
        retryable: bool = False,
        **error_details,
    ) -> None:
        """Mark the task as failed with error details.

        Args:
            error: Error message, exception, or ErrorDetails object
            error_type: Type of error (if error is a string)
            retryable: Whether the task can be retried
            **error_details: Additional error context
        """
        if self.status not in (TaskStatus.PROCESSING, TaskStatus.PENDING):
            raise ValueError(f"Cannot fail task in {self.status} state")

        self.completed_at = datetime.now(UTC)
        self.status = TaskStatus.FAILED

        # Handle different error types
        if isinstance(error, ErrorDetails):
            self.error_details = error
        elif isinstance(error, Exception):
            self.error_details = ErrorDetails(
                error_type=error_type or ErrorType.PROCESSING,
                message=str(error),
                details={
                    **error_details,
                    "exception_type": error.__class__.__name__,
                    "exception_args": (
                        list(error.args) if hasattr(error, "args") else []
                    ),
                },
                retryable=retryable,
            )
        else:
            self.error_details = ErrorDetails(
                error_type=error_type or ErrorType.PROCESSING,
                message=str(error),
                details=error_details,
                retryable=retryable,
            )

        # Log the failure
        logger.error(
            f"Task {self.task_id} failed: {self.error_details.message}",
            task_id=self.task_id,
            task_type=self.task_type,
            error_type=self.error_details.error_type,
            details=self.error_details.details,
            retryable=self.error_details.retryable,
        )

        self._record_progress(
            self.progress,
            f"Task failed: {self.error_details.message}",
            error=self.error_details.to_dict(),
            **error_details,
        )

        self._trigger_callbacks("error")
        self._trigger_callbacks("status_change")

    def mark_cancelled(self, reason: str = "Task was cancelled", **metadata) -> None:
        """Mark the task as cancelled.

        Args:
            reason: Reason for cancellation
            **metadata: Additional metadata
        """
        if self.status not in (TaskStatus.PROCESSING, TaskStatus.PENDING):
            raise ValueError(f"Cannot cancel task in {self.status} state")

        self.completed_at = datetime.now(UTC)
        self.status = TaskStatus.CANCELLED
        self.error_details = ErrorDetails(
            error_type=ErrorType.CANCELLED,
            message=reason,
            details=metadata,
            retryable=False,
        )

        logger.info(
            f"Task {self.task_id} was cancelled: {reason}",
            task_id=self.task_id,
            task_type=self.task_type,
            **metadata,
        )

        self._record_progress(self.progress, f"Task cancelled: {reason}", **metadata)

        self._trigger_callbacks("status_change")

    def is_stalled(self, stall_threshold_seconds: float) -> bool:
        """Check if the task appears to be stalled.

        A task is considered stalled if it's been in the PROCESSING state
        for longer than the specified threshold without any progress updates.

        Args:
            stall_threshold_seconds: Number of seconds without updates to consider the task stalled

        Returns:
            bool: True if the task is stalled, False otherwise
        """
        if self.status != TaskStatus.PROCESSING:
            return False

        if not self._progress_history:
            return False

        last_update = self._progress_history[-1].timestamp
        time_since_update = (datetime.now(UTC) - last_update).total_seconds()
        is_stalled = time_since_update > stall_threshold_seconds

        if is_stalled:
            logger.warning(
                f"Task {self.task_id} appears to be stalled. "
                f"No updates for {time_since_update:.1f}s (threshold: {stall_threshold_seconds}s)",
                task_id=self.task_id,
                task_type=self.task_type,
                time_since_update=time_since_update,
                threshold=stall_threshold_seconds,
                current_progress=self.progress,
            )

        return is_stalled

    def get_execution_time(self) -> float:
        """Get the total execution time in seconds.

        Returns:
            float: Execution time in seconds, or 0 if not started
        """
        if not self.started_at:
            return 0.0

        end_time = self.completed_at or datetime.now(UTC)
        return (end_time - self.started_at).total_seconds()

    def get_remaining_time(self) -> float | None:
        """Estimate the remaining time until task completion.

        Returns:
            Optional[float]: Estimated remaining time in seconds, or None if not enough data
        """
        if not self.started_at or self.expected_duration is None:
            return None

        if self.status in (
            TaskStatus.COMPLETED,
            TaskStatus.FAILED,
            TaskStatus.CANCELLED,
        ):
            return 0.0

        elapsed = self.get_execution_time()

        if self.progress <= 0.0:
            return self.expected_duration

        estimated_total = elapsed / self.progress
        remaining = estimated_total - elapsed

    def add_callback(
        self,
        callback_type: str,
        callback: Callable[[TaskState], None],
        replace: bool = False,
    ) -> None:
        """Register a callback for task events.

        Args:
            callback_type: Type of event to register for
                         ('progress', 'complete', 'error', 'status_change', 'timeout')
            callback: Callback function that takes this TaskState as an argument
            replace: If True, replace all existing callbacks of this type

        Raises:
            ValueError: If callback_type is invalid
        """
        if callback_type not in self._callbacks:
            raise ValueError(
                f"Invalid callback type: {callback_type}. "
                f"Must be one of: {list(self._callbacks.keys())}"
            )

        if replace:
            self._callbacks[callback_type] = [callback]
        else:
            self._callbacks[callback_type].append(callback)

    def remove_callback(
        self, callback_type: str, callback: Callable[[TaskState], None]
    ) -> bool:
        """Remove a registered callback.

        Args:
            callback_type: Type of the callback to remove
            callback: The callback function to remove

        Returns:
            bool: True if callback was found and removed, False otherwise
        """
        if callback_type not in self._callbacks:
            return False

        initial_length = len(self._callbacks[callback_type])
        self._callbacks[callback_type] = [
            cb for cb in self._callbacks[callback_type] if cb != callback
        ]
        return len(self._callbacks[callback_type]) < initial_length

    def to_dict(self) -> dict[str, Any]:
        """Convert the task state to a dictionary.

        Returns:
            Dictionary representation of the task state
        """
        result = {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "status": self.status,
            "progress": self.progress,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": (
                self.completed_at.isoformat() if self.completed_at else None
            ),
            "execution_time": self.get_execution_time(),
            "remaining_time": self.get_remaining_time(),
            "expected_duration": self.expected_duration,
            "error": self.error_details.to_dict() if self.error_details else None,
            "metadata": self.metadata,
        }


class TaskStateTracker:
    """Tracks the state of multiple tasks across the application.

    This class provides a centralized way to manage and monitor the state of all tasks
    in the system, with support for finding stalled tasks and managing task lifecycles.
    """

    """Класс для отслеживания состояния всех задач в системе"""

    # Типовые ожидаемые продолжительности для разных типов задач (в секундах)
    DEFAULT_DURATIONS = {
        "subtitle": 60.0,  # 1 минута для загрузки субтитров
        "summarize": 30.0,  # 30 секунд для суммаризации
        "video_list": 20.0,  # 20 секунд для получения списка видео
    }

    # Пороги для определения зависших задач (в секундах)
    STALL_THRESHOLDS = {
        "subtitle": 120.0,  # 2 минуты без обновлений для задачи субтитров
        "summarize": 60.0,  # 1 минута без обновлений для задачи суммаризации
        "video_list": 40.0,  # 40 секунд без обновлений для задачи списка видео
    }

    def __init__(self):
        self.tasks: dict[str, TaskState] = {}
        self.active_tasks: set[str] = set()  # Задачи в состоянии PROCESSING
        self.stalled_tasks: set[str] = set()  # Зависшие задачи

    def register_task(self, task_id: str, task_type: str) -> TaskState:
        """Регистрирует новую задачу в трекере"""
        if task_id in self.tasks:
            logger.warning(f"Task {task_id} already registered in TaskStateTracker")
            return self.tasks[task_id]

        task_state = TaskState(task_id, task_type)
        self.tasks[task_id] = task_state
        logger.debug(
            f"Registered task {task_id} of type {task_type} in TaskStateTracker"
        )
        return task_state

    def start_task(self, task_id: str):
        """Отмечает задачу как начатую"""
        if task_id not in self.tasks:
            logger.warning(f"Attempted to start unregistered task {task_id}")
            return

        task_state = self.tasks[task_id]
        expected_duration = self.DEFAULT_DURATIONS.get(task_state.task_type)
        task_state.mark_started(expected_duration)
        self.active_tasks.add(task_id)
        logger.debug(
            f"Started task {task_id} with expected duration {expected_duration}s"
        )

    def update_task_progress(self, task_id: str, progress: float):
        """Обновляет прогресс выполнения задачи"""
        if task_id not in self.tasks:
            logger.warning(
                f"Attempted to update progress for unregistered task {task_id}"
            )
            return

        self.tasks[task_id].update_progress(progress)
        # Если задача была в списке зависших, удаляем её оттуда
        if task_id in self.stalled_tasks:
            self.stalled_tasks.remove(task_id)
            logger.debug(f"Task {task_id} is no longer stalled after progress update")

    def complete_task(self, task_id: str):
        """Отмечает задачу как завершенную"""
        if task_id not in self.tasks:
            logger.warning(f"Attempted to complete unregistered task {task_id}")
            return

        self.tasks[task_id].mark_completed()
        self.active_tasks.discard(task_id)
        self.stalled_tasks.discard(task_id)
        logger.debug(f"Completed task {task_id}")

    def fail_task(self, task_id: str, error: str):
        """Отмечает задачу как неудачную"""
        if task_id not in self.tasks:
            logger.warning(f"Attempted to fail unregistered task {task_id}")
            return

        self.tasks[task_id].mark_failed(error)
        self.active_tasks.discard(task_id)
        self.stalled_tasks.discard(task_id)
        logger.debug(f"Failed task {task_id}: {error}")

    def cancel_task(self, task_id: str, reason: str):
        """Отмечает задачу как отмененную"""
        if task_id not in self.tasks:
            logger.warning(f"Attempted to cancel unregistered task {task_id}")
            return

        self.tasks[task_id].mark_cancelled(reason)
        self.active_tasks.discard(task_id)
        self.stalled_tasks.discard(task_id)
        logger.debug(f"Cancelled task {task_id}: {reason}")

    def check_stalled_tasks(self) -> set[str]:
        """Проверяет все активные задачи на предмет зависания"""
        newly_stalled = set()

        for task_id in self.active_tasks:
            task_state = self.tasks[task_id]
            stall_threshold = self.STALL_THRESHOLDS.get(task_state.task_type, 60.0)

            if (
                task_state.is_stalled(stall_threshold)
                and task_id not in self.stalled_tasks
            ):
                self.stalled_tasks.add(task_id)
                newly_stalled.add(task_id)
                execution_time = task_state.get_execution_time() or 0
                logger.warning(
                    f"Task {task_id} of type {task_state.task_type} is stalled. "
                    f"No updates for {time.time() - task_state.last_update_time:.1f}s. "
                    f"Total execution time: {execution_time:.1f}s"
                )

        return newly_stalled

    def get_task_state(self, task_id: str) -> TaskState | None:
        """Возвращает состояние задачи по ID"""
        return self.tasks.get(task_id)

    def get_active_tasks_count(self) -> dict[str, int]:
        """Возвращает количество активных задач по типам"""
        counts = {}
        for task_id in self.active_tasks:
            task_type = self.tasks[task_id].task_type
            counts[task_type] = counts.get(task_type, 0) + 1
        return counts

    def get_stalled_tasks_info(self) -> dict[str, Any]:
        """Возвращает информацию о зависших задачах"""
        result = {"count": len(self.stalled_tasks), "tasks": []}

        for task_id in self.stalled_tasks:
            task_state = self.tasks[task_id]
            execution_time = task_state.get_execution_time() or 0
            time_since_update = time.time() - task_state.last_update_time

            result["tasks"].append(
                {
                    "task_id": task_id,
                    "task_type": task_state.task_type,
                    "execution_time": execution_time,
                    "time_since_update": time_since_update,
                    "progress": task_state.progress,
                }
            )

        return result

    def cleanup_completed_tasks(self, max_age_seconds: float = 3600.0):
        """Удаляет завершенные задачи старше указанного возраста"""
        current_time = time.time()
        tasks_to_remove = []

        for task_id, task_state in self.tasks.items():
            if task_state.status in [
                TaskStatus.COMPLETED,
                TaskStatus.FAILED,
                TaskStatus.CANCELLED,
            ]:
                if current_time - task_state.last_update_time > max_age_seconds:
                    tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.tasks[task_id]

        if tasks_to_remove:
            logger.debug(
                f"Cleaned up {len(tasks_to_remove)} completed tasks older than {max_age_seconds / 3600:.1f} hours"
            )
