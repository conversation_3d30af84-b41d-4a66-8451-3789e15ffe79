import asyncio
import re
import socket
import tempfile
from pathlib import Path
from typing import Any
from xml.etree import ElementTree as ET

import yt_dlp
from loguru import logger

from models.database import Database


class YouTubeSubtitleDownloader:
    # Семафор для ограничения одновременных загрузок
    _download_semaphore = asyncio.Semaphore(2)

    # <-- Add cookies_file parameter
    def __init__(self, proxy_url: str | None = None, cookies_file: str | None = None):
        # Base options
        self.base_opts = {
            "quiet": True,
            "no_warnings": True,
            "skip_download": True,
            "writesubtitles": True,
            "writeautomaticsub": True,
            "subtitlesformat": "ttml",
            "subtitleslangs": ["ru", "en"],
            # Важно: используем video id как имя файла
            "outtmpl": "%(id)s.%(ext)s",
        }
        if proxy_url:
            self.base_opts["proxy"] = proxy_url
            logger.debug(f"Initialized with proxy: {proxy_url}")

        self.cookies_file = cookies_file  # <-- Store cookies_file
        if self.cookies_file:
            logger.debug(f"Initialized with cookies file: {self.cookies_file}")

        self.db = Database()

    @staticmethod
    def validate_youtube_url(url: str) -> bool:
        """Validate that URL is from youtube.com or youtu.be"""
        try:
            from urllib.parse import urlparse

            parsed = urlparse(url)
            return parsed.netloc in ["youtube.com", "www.youtube.com", "youtu.be"]
        except Exception:
            return False

    @staticmethod
    def clean_youtube_url(url: str) -> str:
        """Clean YouTube URL by removing playlist and other unnecessary parameters.
        Returns a clean URL with only the video ID parameter."""
        try:
            from urllib.parse import parse_qs, urlencode, urlparse, urlunparse

            # Проверяем, что это URL YouTube
            if not YouTubeSubtitleDownloader.validate_youtube_url(url):
                return url  # Возвращаем исходный URL, если это не YouTube

            parsed_url = urlparse(url)

            # Для коротких ссылок youtu.be
            if parsed_url.netloc == "youtu.be":
                # Извлекаем ID из пути
                path = parsed_url.path.lstrip("/")
                video_id = path.split("/")[0]

                # Создаем новый URL на youtube.com
                clean_parsed = parsed_url._replace(
                    netloc="www.youtube.com",
                    path="/watch",
                    query=urlencode({"v": video_id}),
                )
                clean_url = urlunparse(clean_parsed)
                logger.debug(f"Cleaned youtu.be URL: {url} -> {clean_url}")
                return clean_url

            # Для стандартных ссылок youtube.com
            query_params = parse_qs(parsed_url.query)

            # Если ID находится в параметре 'v'
            if "v" in query_params:
                video_id = query_params["v"][0]
                # Создаем новый URL только с параметром v
                clean_parsed = parsed_url._replace(query=urlencode({"v": video_id}))
                clean_url = urlunparse(clean_parsed)
                logger.debug(f"Cleaned youtube.com URL: {url} -> {clean_url}")
                return clean_url

            # Если не удалось очистить URL, возвращаем исходный
            return url

        except Exception as e:
            logger.warning(f"Failed to clean YouTube URL: {str(e)}")
            return url  # В случае ошибки возвращаем исходный URL

    @staticmethod
    def extract_video_id(url: str) -> str:
        """Extract video ID from YouTube URL with validation."""
        try:
            # First validate that this is a YouTube URL
            if not YouTubeSubtitleDownloader.validate_youtube_url(url):
                raise ValueError("URL must be from youtube.com or youtu.be domains")

            # Обработка коротких ссылок youtu.be
            from urllib.parse import parse_qs, urlparse

            parsed_url = urlparse(url)

            # Для коротких ссылок youtu.be ID находится в пути URL
            if parsed_url.netloc == "youtu.be":
                # Извлекаем ID из пути, удаляя начальный слеш
                path = parsed_url.path.lstrip("/")
                # Очищаем от дополнительных параметров в пути
                video_id = path.split("/")[0]
                if not video_id:
                    raise ValueError("Video ID cannot be empty in youtu.be URL")
                logger.debug(f"Extracted video ID from youtu.be URL: {video_id}")
                return video_id

            # Для стандартных ссылок youtube.com
            query_params = parse_qs(parsed_url.query)

            # Если ID находится в параметре 'v'
            if "v" in query_params:
                video_id = query_params["v"][0]
                if not video_id:
                    raise ValueError("Video ID cannot be empty")
                logger.debug(f"Extracted video ID from query parameter: {video_id}")
                return video_id

            # Если ID не найден в параметре 'v', пробуем другие шаблоны
            patterns = [
                r"(?:v=|\/videos\/|embed\/|youtu\.be\/|\/v\/|\/e\/|watch\?v=|&v=)([^#\&\?\n]*)",
            ]
            for pattern in patterns:
                match = re.search(pattern, url)
                if match and match.group(1):
                    video_id = match.group(1)
                    if not video_id:
                        raise ValueError("Video ID cannot be empty")
                    logger.debug(
                        f"Extracted video ID using regex: {video_id} from URL: {url}"
                    )
                    return video_id

            # Если ID не найден ни одним из способов
            raise ValueError(
                "Could not extract video ID from URL. URL must be a valid YouTube video URL"
            )

        except ValueError as e:
            # Re-raise ValueError with same message
            raise ValueError(f"Invalid YouTube URL: {str(e)}")
        except Exception as e:
            # Wrap other exceptions
            raise ValueError(f"Invalid YouTube URL: {str(e)}")

    @staticmethod
    def clean_ttml(ttml_content: str) -> str:
        """Extract plain text from TTML format."""
        try:
            logger.debug("Parsing TTML content")
            root = ET.fromstring(ttml_content)
            ns = {"tt": "http://www.w3.org/ns/ttml"}
            texts = []

            # Проверяем структуру TTML
            logger.debug(f"TTML root tag: {root.tag}")
            logger.debug(
                f"TTML namespaces: {root.nsmap if hasattr(root, 'nsmap') else 'No nsmap'}"
            )

            # Пробуем разные варианты поиска текста
            for p in root.findall(".//tt:p", ns):
                text = "".join(p.itertext()).strip()
                if text:
                    texts.append(text)
                    # logger.debug(f"Found text segment: {text[:50]}...")

            if not texts:
                logger.warning("No text found using tt:p, trying alternative parsing")
                # Пробуем найти текст без namespace
                for elem in root.iter():
                    if elem.text and elem.text.strip():
                        texts.append(elem.text.strip())
                        logger.debug(
                            f"Found text using alternative method: {elem.text[:50]}..."
                        )

            result = "\n".join(texts)
            logger.debug(f"Extracted {len(texts)} text segments")
            return result

        except ET.ParseError as e:
            logger.error(f"Failed to parse TTML: {str(e)}")
            logger.debug("Falling back to regex cleaning")
            # Fallback к простой очистке тегов
            clean_text = re.sub(r"<[^>]+>", "", ttml_content)
            clean_text = re.sub(r"\s+", " ", clean_text).strip()
            logger.debug(f"Cleaned text length: {len(clean_text)}")
            return clean_text

    @staticmethod
    def extract_video_ids(url: str) -> list[str]:
        """Extract video IDs from YouTube URL (video, playlist, or channel)."""
        try:
            # Validate that this is a YouTube URL
            if not YouTubeSubtitleDownloader.validate_youtube_url(url):
                raise ValueError("URL must be from youtube.com or youtu.be domains")

            # Преобразуем URL канала в URL видео, если необходимо
            if "/@" in url and "/videos" not in url:
                url = url + "/videos"
            logger.debug(f"Processing URL: {url}")

            # Configure yt-dlp options
            ydl_opts = {
                "quiet": True,
                "no_warnings": True,
                "ignoreerrors": True,  # Пропускать ошибки для отдельных видео
                "extract_flat": "in_playlist",  # Извлекать информацию о видео в плейлистах # True
                "playlistreverse": False,  # Не изменять порядок видео
                # "playlistend": 50,  # Ограничение количества видео для избежания перегрузки
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Извлекаем информацию
                info = ydl.extract_info(url, download=False)

                video_ids = []

                # Обработка информации в зависимости от типа URL
                if info.get("_type") in ["playlist", "channel", "multi_video"]:
                    # Для плейлистов, каналов и списков видео
                    entries = info.get("entries", [])
                    for entry in entries:
                        if entry and entry.get("id"):
                            video_ids.append(entry["id"])
                        elif entry and entry.get("url"):
                            # Для случаев, когда ID находится в URL
                            try:
                                entry_url = entry["url"]
                                if "v=" in entry_url:
                                    video_id = entry_url.split("v=")[1].split("&")[0]
                                    video_ids.append(video_id)
                            except Exception:
                                continue
                else:
                    # Для одиночного видео
                    if info.get("id"):
                        video_ids.append(info["id"])
                    elif info.get("url") and "v=" in info.get("url"):
                        video_id = info["url"].split("v=")[1].split("&")[0]
                        video_ids.append(video_id)

                return list(dict.fromkeys(video_ids))  # Удаляем дубликаты

        except Exception as e:
            logger.error(f"Error extracting video IDs: {str(e)}")
            raise ValueError(f"Failed to extract video IDs: {str(e)}")

    @staticmethod
    def normalize_language_code(lang_code: str) -> str:
        """Normalize language code to standard format."""
        # Remove -orig and .orig suffix and normalize common language codes
        lang_code = lang_code.replace("-orig", "").replace(".orig", "")
        # Remove region codes (e.g. en-US -> en)
        if "-" in lang_code:
            lang_code = lang_code.split("-")[0]
        return lang_code

    async def download_subtitles(
        self, url: str
    ) -> tuple[str | None, str | None, str | None, str | None, str | None]:
        """Download and process subtitles for both languages. Returns (title, original_language, publish_date, en_subs, ru_subs)"""
        # Очищаем URL от параметров плейлиста и других ненужных параметров
        clean_url = self.clean_youtube_url(url)
        logger.debug(f"Using cleaned URL for download: {clean_url}")

        # Извлекаем video_id из оригинального URL для сохранения совместимости
        video_id = self.extract_video_id(url)

        # Используем семафор для ограничения одновременных загрузок
        async with self._download_semaphore:
            # Check cache first
            (
                cached_title,
                cached_original_language,
                cached_publish_date,
                cached_en,
                cached_ru,
            ) = await self.db.get_subtitles(video_id)
            if cached_en or cached_ru:
                logger.info(f"Found cached subtitles for video {video_id}")
                return (
                    cached_title,
                    cached_original_language,
                    cached_publish_date,
                    cached_en,
                    cached_ru,
                )

            # If not in cache, download subtitles
            temp_dir = tempfile.mkdtemp()
            logger.debug(f"Created temp directory: {temp_dir}")

            en_subs = ru_subs = None
            title = None
            original_language = None
            publish_date = None

        try:
            # Configure yt-dlp with modified options to ensure we get auto-generated subtitles
            ydl_opts = self.base_opts.copy()
            ydl_opts["paths"] = {"home": temp_dir}
            # Explicitly set these options to ensure we get auto-generated subtitles
            ydl_opts["writeautomaticsub"] = True
            ydl_opts["writesubtitles"] = True
            # Force download of both auto and manual subtitles with regional codes
            ydl_opts["subtitleslangs"] = [
                "en-orig",
                "en.orig",
                "en",
                "en-US",
                "ru",
                "ru-RU",
            ]
            # Add postprocessor to ensure conversion to ttml format
            ydl_opts["postprocessors"] = [
                {
                    "key": "FFmpegSubtitlesConvertor",
                    "format": "ttml",
                }
            ]

            # --- Add this block ---
            if self.cookies_file:
                ydl_opts["cookiesfromfile"] = self.cookies_file
                logger.debug(f"Added cookiesfromfile option: {self.cookies_file}")
            # --- End of added block ---

            # Создаем функцию для выполнения блокирующих операций
            def _download_and_process():
                # ydl_opts now includes cookiesfromfile if set
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    # Get video info
                    logger.debug(f"Extracting video info for {clean_url}")
                    info: dict[str, Any] = ydl.extract_info(clean_url, download=False)

                    # Extract video title, publish date and detect original language
                    nonlocal title, original_language, publish_date
                    title = info.get("title")
                    upload_date = info.get("upload_date")  # Формат YYYYMMDD
                    # Преобразуем дату в более читаемый формат YYYY-MM-DD
                    publish_date = (
                        f"{upload_date[:4]}-{upload_date[4:6]}-{upload_date[6:]}"
                        if upload_date
                        else None
                    )

                    # Detect original language using existing methods
                    if info.get("automatic_captions"):
                        auto_langs = [
                            lang
                            for lang in info.get("automatic_captions", {}).keys()
                            if lang != "live_chat"
                        ]
                        # Сначала ищем оригинальный язык с пометкой orig
                        orig_lang = next(
                            (
                                lang
                                for lang in auto_langs
                                if "-orig" in lang or ".orig" in lang
                            ),
                            None,
                        )
                        if orig_lang:
                            original_language = self.normalize_language_code(orig_lang)
                            logger.info(
                                f"Detected original language from orig suffix: {original_language}"
                            )
                        elif auto_langs:
                            # Если нет пометки orig, берем первый язык из списка
                            original_language = self.normalize_language_code(
                                auto_langs[0]
                            )
                            logger.info(
                                f"Using first available language as original: {original_language}"
                            )

                    if not original_language and info.get("language"):
                        original_language = self.normalize_language_code(
                            info.get("language")
                        )

                    # Download subtitles
                    logger.debug("Downloading subtitles")
                    ydl.download([clean_url])

                    return info

            logger.debug(f"Current socket type: {type(socket.socket)}")
            logger.debug(f"yt-dlp options: {ydl_opts}")

            # Выполняем блокирующие операции в отдельном потоке через run_in_executor с повторными попытками
            loop = asyncio.get_running_loop()
            max_retries = 3
            retry_delay = 1.0  # начальная задержка в секундах

            for attempt in range(max_retries):
                try:
                    info = await loop.run_in_executor(None, _download_and_process)
                    break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise  # Если все попытки исчерпаны, пробрасываем исключение
                    logger.warning(
                        f"Попытка {attempt + 1} не удалась: {str(e)}. Повторная попытка через {retry_delay} сек."
                    )
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Увеличиваем задержку экспоненциально

            # Log available subtitles
            if "subtitles" in info:
                logger.info(
                    f"Available manual subtitles: {list(info['subtitles'].keys())}"
                )
            if "automatic_captions" in info:
                logger.info(
                    f"Available auto subtitles: {list(info['automatic_captions'].keys())}"
                )

            # Дополнительные методы определения языка
            if not original_language and info.get("formats"):
                audio_formats = [
                    f
                    for f in info.get("formats", [])
                    if f.get("vcodec") == "none" and f.get("acodec") != "none"
                ]
                for fmt in audio_formats:
                    if fmt.get("language") and fmt.get("language") != "none":
                        original_language = self.normalize_language_code(
                            fmt.get("language")
                        )
                        logger.info(
                            f"Detected original language from audio format: {original_language}"
                        )
                        break

            # Проверка субтитров
            if not original_language and info.get("subtitles"):
                for lang, subs in info.get("subtitles", {}).items():
                    for sub in subs:
                        if isinstance(sub, dict) and (
                            sub.get("is_original") or sub.get("is_default")
                        ):
                            original_language = self.normalize_language_code(lang)
                            logger.info(
                                f"Detected original language from default subtitles: {original_language}"
                            )
                            break
                    if original_language:
                        break

            # Определение языка по описанию
            if not original_language:
                description = info.get("description", "")
                if description:
                    cyrillic_count = len(re.findall(r"[а-яА-Я]", description))
                    latin_count = len(re.findall(r"[a-zA-Z]", description))

                    if cyrillic_count > latin_count * 2:
                        original_language = "ru"
                        logger.info(
                            "Inferred Russian language from description content"
                        )
                    elif latin_count > cyrillic_count * 2:
                        original_language = "en"
                        logger.info(
                            "Inferred English language from description content"
                        )

            # Создаем функцию для обработки файлов субтитров
            def process_subtitle_files():
                nonlocal en_subs, ru_subs
                subtitle_files = list(Path(temp_dir).glob(f"{video_id}*.ttml"))
                logger.info(
                    f"Found {len(subtitle_files)} subtitle files: {[f.name for f in subtitle_files]}"
                )

                manual_en = manual_ru = auto_en = auto_ru = None

                for file in subtitle_files:
                    try:
                        logger.debug(f"Processing subtitle file: {file}")
                        with open(file, encoding="utf-8") as f:
                            content = f.read()
                            logger.debug(f"Read {len(content)} bytes from {file}")

                            cleaned_text = self.clean_ttml(content)
                            logger.debug(f"Cleaned text length: {len(cleaned_text)}")

                            # Categorize the subtitle file
                            is_auto = ".auto." in file.name
                            is_orig = (
                                "-orig" in file.name.lower()
                                or ".orig" in file.name.lower()
                            )
                            # Улучшенное определение языка с учетом региональных кодов
                            is_en = any(
                                code in file.name.lower()
                                for code in ["en", "en-us", "en_us"]
                            )
                            is_ru = any(
                                code in file.name.lower()
                                for code in ["ru", "ru-ru", "ru_ru"]
                            )

                            logger.debug(
                                f"Analyzing subtitle file: auto={is_auto}, orig={is_orig}, en={is_en}, ru={is_ru}"
                            )

                            # Приоритет: оригинальные > ручные > автоматические субтитры
                            if is_en:
                                if is_orig:
                                    manual_en = cleaned_text
                                    logger.info(
                                        f"Found English original subtitles in {file.name}"
                                    )
                                elif not is_auto and not manual_en:
                                    manual_en = cleaned_text
                                    logger.info(
                                        f"Found English manual subtitles in {file.name}"
                                    )
                                elif is_auto and not manual_en and not auto_en:
                                    auto_en = cleaned_text
                                    logger.info(
                                        f"Found English automatic subtitles in {file.name}"
                                    )
                            elif is_ru:
                                if is_orig:
                                    manual_ru = cleaned_text
                                    logger.info(
                                        f"Found Russian original subtitles in {file.name}"
                                    )
                                elif not is_auto and not manual_ru:
                                    manual_ru = cleaned_text
                                    logger.info(
                                        f"Found Russian manual subtitles in {file.name}"
                                    )
                                elif is_auto and not manual_ru and not auto_ru:
                                    auto_ru = cleaned_text
                                    logger.info(
                                        f"Found Russian automatic subtitles in {file.name}"
                                    )
                    except Exception as e:
                        logger.error(
                            f"Error processing subtitle file {file}: {e}",
                            exc_info=True,
                        )

                # Prioritize manual subtitles over automatic ones
                en_subs = manual_en if manual_en else auto_en
                ru_subs = manual_ru if manual_ru else auto_ru

                # If original language is Russian but we don't have Russian subs, try harder
                if original_language == "ru" and not ru_subs:
                    logger.warning(
                        "Original language is Russian but no Russian subtitles found. Trying again with specific options."
                    )
                    # Try again with more specific options for Russian auto-generated subtitles
                    specific_opts = ydl_opts.copy()
                    specific_opts["subtitleslangs"] = ["ru"]
                    with yt_dlp.YoutubeDL(specific_opts) as specific_ydl:
                        specific_ydl.download([clean_url])
                        # Check for new Russian subtitle files
                        ru_files = list(Path(temp_dir).glob(f"{video_id}*ru*.ttml"))
                        for file in ru_files:
                            try:
                                if file.name not in [
                                    f.name for f in subtitle_files
                                ]:  # Only process new files
                                    logger.debug(
                                        f"Processing additional Russian subtitle file: {file}"
                                    )
                                    with open(file, encoding="utf-8") as f:
                                        content = f.read()
                                        cleaned_text = self.clean_ttml(content)
                                        ru_subs = cleaned_text
                                        logger.info(
                                            f"Found additional Russian subtitles in {file.name}"
                                        )
                                        break
                            except Exception as e:
                                logger.error(
                                    f"Error processing additional subtitle file {file}: {e}",
                                    exc_info=True,
                                )

            # Выполняем обработку файлов субтитров асинхронно
            await loop.run_in_executor(None, process_subtitle_files)

            # Проверяем наличие субтитров после обработки
            if not en_subs and not ru_subs:
                logger.warning(
                    "No subtitles found after initial processing, trying with extended options"
                )
                # Пробуем скачать с расширенными опциями
                extended_opts = ydl_opts.copy()
                extended_opts["writeautomaticsub"] = True
                extended_opts["writesubtitles"] = True
                extended_opts["subtitleslangs"] = [
                    "en",
                    "ru",
                    "en-orig",
                    "ru-orig",
                    "en.orig",
                    "ru.orig",
                ]

                async def try_extended_download():
                    nonlocal en_subs, ru_subs
                    await loop.run_in_executor(
                        None,
                        lambda: yt_dlp.YoutubeDL(extended_opts).download([clean_url]),
                    )
                    await loop.run_in_executor(None, process_subtitle_files)

                try:
                    await try_extended_download()
                except Exception as e:
                    logger.error(f"Failed extended subtitle download attempt: {e}")

            if en_subs or ru_subs:
                # Save to database if we got any subtitles
                await self.db.save_subtitles(
                    video_id, title, original_language, publish_date, en_subs, ru_subs
                )
                logger.info(
                    f"Saved subtitles with original language: {original_language}"
                )
            else:
                logger.error(f"No subtitles found for video {video_id}")

            return title, original_language, publish_date, en_subs, ru_subs

        except Exception as e:
            logger.error(f"Error downloading subtitles: {str(e)}", exc_info=True)
            return None, None, None, None, None
        finally:
            # Cleanup temporary files
            logger.debug(f"Cleaning up temp directory: {temp_dir}")
            for file in Path(temp_dir).glob("*"):
                try:
                    file.unlink()
                    logger.debug(f"Deleted file: {file}")
                except Exception as e:
                    logger.warning(f"Failed to delete temporary file {file}: {str(e)}")
            try:
                Path(temp_dir).rmdir()
                logger.debug("Temp directory removed")
            except Exception as e:
                logger.warning(f"Failed to delete temporary directory: {str(e)}")
