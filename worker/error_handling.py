from enum import Enum
from typing import Any

import structlog
from loguru import logger as loguru_logger
from pydantic import BaseModel

# Use structured logger by default, fall back to loguru if not available
try:
    logger = structlog.get_logger(__name__)
except Exception:
    logger = loguru_logger


class ErrorType(str, Enum):
    """Types of errors that can occur in the application"""

    VALIDATION = "validation_error"
    NETWORK = "network_error"
    EXTERNAL_SERVICE = "external_service_error"
    PROCESSING = "processing_error"
    TIMEOUT = "timeout_error"
    RATE_LIMIT = "rate_limit_exceeded"
    NOT_FOUND = "not_found"
    UNAUTHORIZED = "unauthorized"
    INTERNAL = "internal_error"
    CANCELLED = "cancelled"


class ErrorDetails(BaseModel):
    """Structured error details for consistent error handling"""

    error_type: ErrorType
    message: str
    details: dict[str, Any] | None = None
    retryable: bool = False

    def to_dict(self) -> dict[str, Any]:
        """Convert error details to a dictionary"""
        return {
            "error_type": self.error_type,
            "message": self.message,
            "details": self.details,
            "retryable": self.retryable,
        }


class TaskError(Exception):
    """Base exception for task-related errors"""

    def __init__(
        self,
        message: str,
        error_type: ErrorType = ErrorType.PROCESSING,
        details: dict[str, Any] | None = None,
        retryable: bool = False,
        original_exception: Exception | None = None,
    ):
        self.error_details = ErrorDetails(
            error_type=error_type,
            message=message,
            details=details or {},
            retryable=retryable,
        )
        self.original_exception = original_exception
        super().__init__(message)

    @classmethod
    def from_exception(
        cls,
        exc: Exception,
        error_type: ErrorType | None = None,
        message: str | None = None,
        **kwargs,
    ) -> "TaskError":
        """Create a TaskError from an existing exception"""
        if isinstance(exc, TaskError):
            return exc

        error_type = error_type or ErrorType.PROCESSING
        message = message or f"An error occurred: {str(exc) or 'Unknown error'}"

        return cls(
            message=message, error_type=error_type, original_exception=exc, **kwargs
        )


class ValidationError(TaskError):
    """Raised when input validation fails"""

    def __init__(self, message: str, details: dict[str, Any] | None = None):
        super().__init__(
            message=message,
            error_type=ErrorType.VALIDATION,
            details=details or {},
            retryable=False,
        )


class NetworkError(TaskError):
    """Raised for network-related errors"""

    def __init__(self, message: str, details: dict[str, Any] | None = None):
        super().__init__(
            message=message,
            error_type=ErrorType.NETWORK,
            details=details or {},
            retryable=True,
        )


class ExternalServiceError(TaskError):
    """Raised when an external service fails"""

    def __init__(
        self, message: str, service_name: str, details: dict[str, Any] | None = None
    ):
        details = details or {}
        details["service"] = service_name
        super().__init__(
            message=message,
            error_type=ErrorType.EXTERNAL_SERVICE,
            details=details,
            retryable=True,
        )


class TaskTimeoutError(TaskError):
    """Raised when a task times out"""

    def __init__(self, message: str, timeout_seconds: float):
        super().__init__(
            message=message,
            error_type=ErrorType.TIMEOUT,
            details={"timeout_seconds": timeout_seconds},
            retryable=True,
        )


class RateLimitError(TaskError):
    """Raised when rate limits are exceeded"""

    def __init__(self, message: str, retry_after: int | None = None):
        super().__init__(
            message=message,
            error_type=ErrorType.RATE_LIMIT,
            details={"retry_after": retry_after} if retry_after else None,
            retryable=True,
        )


def format_error_for_response(error: Exception) -> dict[str, Any]:
    """Format an exception for API response"""
    if isinstance(error, TaskError):
        return error.error_details.to_dict()

    return {
        "error_type": ErrorType.INTERNAL,
        "message": str(error) or "An unexpected error occurred",
        "details": {},
        "retryable": False,
    }


def log_error(error: Exception, task_id: str | None = None, **context):
    """Log an error with context using structured logging"""
    log_context = {"task_id": task_id, **context}

    if isinstance(error, TaskError):
        error_data = {
            "event": "task_error",
            "error_message": str(error),
            "error_type": error.error_details.error_type,
            "retryable": error.error_details.retryable,
            "error_details": error.error_details.details,
            **log_context,
        }

        if error.original_exception:
            error_data["original_exception"] = str(error.original_exception)
            error_data["original_exception_type"] = type(
                error.original_exception
            ).__name__

        # Use structured logging if available, otherwise fall back to loguru
        if hasattr(logger, "error") and hasattr(logger, "bind"):
            logger.error(**error_data)
        else:
            loguru_logger.opt(
                exception=(
                    error.original_exception if error.original_exception else error
                )
            ).error(
                f"Task error: {error}",
                **log_context,
                error_type=error.error_details.error_type,
                retryable=error.error_details.retryable,
            )
    else:
        error_data = {
            "event": "unexpected_error",
            "error_message": str(error),
            "error_type": ErrorType.INTERNAL,
            "exception_type": type(error).__name__,
            "retryable": False,
            **log_context,
        }

        # Use structured logging if available, otherwise fall back to loguru
        if hasattr(logger, "error") and hasattr(logger, "bind"):
            logger.error(**error_data)
        else:
            loguru_logger.opt(exception=error).error(
                "Unexpected error",
                **log_context,
                error_type=ErrorType.INTERNAL,
                retryable=False,
            )
