"""API endpoints for text summarization operations."""

import asyncio
import logging

from fastapi import File, Form, UploadFile, status
from fastapi.responses import JSONResponse

from models.schemas import (
    SummarizeMode,
    SummarizeRequest,
    SummarizeResponse,
    TaskStatus,
)

from .base import BaseRouter

logger = logging.getLogger(__name__)


class SummarizeRouter(BaseRouter):
    """Router for text summarization endpoints."""

    def __init__(self):
        super().__init__(prefix="/summarize", tags=["Summarization"])
        self._setup_routes()

    def _setup_routes(self) -> None:
        """Set up the routes for this router."""
        self.router.add_api_route(
            "",
            self.create_summarize_task,
            methods=["POST"],
            response_model=SummarizeResponse,
            status_code=status.HTTP_202_ACCEPTED,
        )

        self.router.add_api_route(
            "/file",
            self.create_summarize_file_task,
            methods=["POST"],
            response_model=SummarizeResponse,
            status_code=status.HTTP_202_ACCEPTED,
        )

    async def create_summarize_task(self, request: SummarizeRequest) -> JSONResponse:
        """
        Submit text for summarization.

        Args:
            request: The request containing the text to summarize and options.

        Returns:
            JSONResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if request.client_uid:
            logger.debug(
                f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
            )

        logger.debug(f"Received /summarize request with mode: {request.mode}")

        task_queue = self._get_task_queue()

        # Check if server is overloaded
        if await self._check_overload():
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is currently overloaded. Please try again later.",
                status=TaskStatus.FAILED,
            )

        # Check if summarize queue is full
        if task_queue.summarize_queue.full():
            logger.debug("Summarize task queue is full")
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Summarize task queue is full. Please try again later.",
                status=TaskStatus.FAILED,
            )

        try:
            # Add task to queue
            response = await asyncio.wait_for(
                task_queue.add_summarize_task(
                    text=request.text,
                    mode=request.mode,
                    client_uid=None,  # Explicitly set to None - deprecated field
                ),
                timeout=5.0,
            )

            logger.debug(
                f"Summarize task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            # Return appropriate status code based on task status
            status_code = (
                status.HTTP_200_OK
                if response.status == TaskStatus.COMPLETED
                else status.HTTP_202_ACCEPTED
            )

            return JSONResponse(status_code=status_code, content=response.model_dump())

        except TimeoutError:
            logger.error("Timeout adding summarize task to queue", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is busy, please try again later",
                status=TaskStatus.FAILED,
            )

        except Exception as e:
            logger.error(f"Error processing summarize request: {e}", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error=f"Error processing request: {str(e)}",
                status=TaskStatus.FAILED,
            )

    async def create_summarize_file_task(
        self,
        file: UploadFile = File(...),
        mode: SummarizeMode | None = Form(None),
        client_uid: str | None = Form(
            None
        ),  # Deprecated: kept for backward compatibility only
    ) -> JSONResponse:
        """
        Submit a file for text summarization.

        Args:
            file: The file to summarize.
            mode: The summarization mode.
            client_uid: Optional client UID for tracking (DEPRECATED - ignored in processing).

        Returns:
            JSONResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if client_uid:
            logger.debug(
                f"Received deprecated client_uid: {client_uid} (ignored for processing)"
            )

        logger.debug(
            f"Received /summarize/file request with mode: {mode}, filename: {file.filename}"
        )

        task_queue = self._get_task_queue()

        # Check if server is overloaded
        if await self._check_overload():
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is currently overloaded. Please try again later.",
                status=TaskStatus.FAILED,
            )

        # Check if summarize queue is full
        if task_queue.summarize_queue.full():
            logger.debug("Summarize task queue is full")
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Summarize task queue is full. Please try again later.",
                status=TaskStatus.FAILED,
            )

        try:
            # Read file content
            content = await file.read()

            # Add task to queue
            response = await asyncio.wait_for(
                task_queue.add_summarize_task(
                    text=content.decode("utf-8"),
                    mode=mode,
                    client_uid=None,  # Explicitly set to None - deprecated field
                    file_name=file.filename,
                ),
                timeout=5.0,
            )

            logger.debug(
                f"Summarize file task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            # Return appropriate status code based on task status
            status_code = (
                status.HTTP_200_OK
                if response.status == TaskStatus.COMPLETED
                else status.HTTP_202_ACCEPTED
            )

            return JSONResponse(status_code=status_code, content=response.model_dump())

        except TimeoutError:
            logger.error("Timeout adding summarize file task to queue", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is busy, please try again later",
                status=TaskStatus.FAILED,
            )

        except UnicodeDecodeError:
            logger.error("Failed to decode file content as UTF-8", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                error="File must be a text file encoded in UTF-8",
                status=TaskStatus.FAILED,
            )

        except Exception as e:
            logger.error(f"Error processing summarize file request: {e}", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error=f"Error processing file: {str(e)}",
                status=TaskStatus.FAILED,
            )


# Create router instance
router = SummarizeRouter().router
