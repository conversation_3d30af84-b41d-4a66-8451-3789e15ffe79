"""Base router for API endpoints."""

import logging

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from worker.queue import TaskQueue

# Get logger instance
logger = logging.getLogger(__name__)


class BaseRouter:
    """Base router class with common functionality for all API endpoints."""

    def __init__(self, prefix: str = "", tags: list[str] | None = None):
        self.router = APIRouter(prefix=prefix, tags=tags or [])
        self.task_queue: TaskQueue | None = None

    def set_task_queue(self, task_queue: TaskQueue) -> None:
        """Set the task queue instance for this router."""
        self.task_queue = task_queue

    async def _check_overload(self) -> bool:
        """Check if the server is overloaded."""
        if not self.task_queue:
            raise HTTPException(status_code=500, detail="Task queue not initialized")

        queue_sizes = self.task_queue.get_queue_sizes()
        logger.debug(f"Queue sizes: {queue_sizes}")

        # Check if any queue is at or over the limit
        for queue_name, size in queue_sizes.items():
            limit = getattr(
                self.task_queue, f"LIMIT_{queue_name.upper()}_QUEUE_SIZE", 100
            )
            if size >= limit:
                logger.warning(f"Queue {queue_name} is overloaded: {size}/{limit}")
                return True

        return False

    def _create_error_response(
        self,
        status_code: int,
        error: str,
        task_id: str = "error",
        status: str = "failed",
    ) -> JSONResponse:
        """Create a standardized error response."""
        return JSONResponse(
            status_code=status_code,
            content={"error": error, "task_id": task_id, "status": status},
        )

    def _get_task_queue(self) -> TaskQueue:
        """Get the task queue instance."""
        if not self.task_queue:
            raise HTTPException(status_code=500, detail="Task queue not initialized")
        return self.task_queue
