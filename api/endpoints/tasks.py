"""API endpoints for task-related operations."""

import logging

from fastapi import Path, status
from fastapi.responses import JSONResponse

from models.schemas import TaskResponse, TaskStatus

from .base import BaseRouter

logger = logging.getLogger(__name__)


class TaskRouter(BaseRouter):
    """Router for task-related endpoints."""

    def __init__(self):
        super().__init__(prefix="/tasks", tags=["Tasks"])
        self._setup_routes()

    def _setup_routes(self) -> None:
        """Set up the routes for this router."""
        self.router.add_api_route(
            "/{task_id}",
            self.get_task,
            methods=["GET"],
            response_model=TaskResponse,
            status_code=status.HTTP_200_OK,
        )

    async def get_task(
        self, task_id: str = Path(..., description="The ID of the task to retrieve")
    ) -> JSONResponse:
        """
        Get the status or result of a task (subtitles or summarization).

        Args:
            task_id: The ID of the task to retrieve.

        Returns:
            JSONResponse: The task status and result if available.
        """
        logger.debug(f"Received request for task status: {task_id}")

        task_queue = self._get_task_queue()

        try:
            # Try to get task status from the queue
            task_status = await task_queue.get_task_status(task_id)

            if task_status is None:
                logger.warning(f"Task not found: {task_id}")
                return self._create_error_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    error=f"Task {task_id} not found",
                    status=TaskStatus.NOT_FOUND,
                )

            logger.debug(f"Task {task_id} status: {task_status.status}")

            # Return task status and result if available
            response_data = {
                "task_id": task_id,
                "status": task_status.status,
                "progress": task_status.progress,
                "result": task_status.result,
                "error": task_status.error,
                "created_at": (
                    task_status.created_at.isoformat()
                    if task_status.created_at
                    else None
                ),
                "updated_at": (
                    task_status.updated_at.isoformat()
                    if task_status.updated_at
                    else None
                ),
                "completed_at": (
                    task_status.completed_at.isoformat()
                    if task_status.completed_at
                    else None
                ),
            }

            # Clean up None values
            response_data = {k: v for k, v in response_data.items() if v is not None}

            return JSONResponse(status_code=status.HTTP_200_OK, content=response_data)

        except Exception as e:
            logger.error(f"Error getting task status for {task_id}: {e}", exc_info=True)
            return self._create_error_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error=f"Error getting task status: {str(e)}",
                status=TaskStatus.FAILED,
            )


# Create router instance
router = TaskRouter().router
