"""TTML to TXT conversion endpoints."""

from typing import Any

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger

from api.middleware.auth import get_current_user
from api.middleware.error_handler import ValidationError
from models.schemas import (
    ConvertTTMLRequest,
    ConvertTTMLResponse,
    TaskStatus,
)
from services.converter_service import ConverterService

router = APIRouter()

# Флаг для отключения требования авторизации для эндпоинта /convert/ttml-to-txt
# Установите в True, чтобы отключить требование авторизации; False, чтобы включить
DISABLE_AUTH_FOR_TTML_CONVERSION = True

security = HTTPBearer(auto_error=False)


async def conditional_auth(
    credentials: HTTPAuthorizationCredentials | None = Depends(security),
) -> dict[str, Any] | None:
    """
    Dependency для условной аутентификации.
    Если DISABLE_AUTH_FOR_TTML_CONVERSION=True, аутентификация не требуется.
    Если False, требуется валидный токен.
    """
    if DISABLE_AUTH_FOR_TTML_CONVERSION:
        # Аутентификация отключена - возвращаем None
        return None
    else:
        # Аутентификация включена - требуем валидный токен
        if not credentials:
            from api.middleware.auth import AuthError

            raise AuthError("API key required")

        # Используем существующую логику аутентификации
        return await get_current_user(credentials)


@router.post("/convert/ttml-to-txt", response_model=ConvertTTMLResponse)
async def convert_ttml_to_txt(
    request: ConvertTTMLRequest,
    user: dict[str, Any] | None = Depends(conditional_auth),
):
    """
    Convert TTML content to TXT format.

    This endpoint performs synchronous conversion of TTML (Timed Text Markup Language)
    content to plain text format. The conversion is fast and doesn't require queuing.

    Authentication: Currently disabled (DISABLE_AUTH_FOR_TTML_CONVERSION=True).
    When enabled, requires write permission.
    """
    logger.debug("Received /convert/ttml-to-txt request")

    try:
        # Create converter service
        converter_service = ConverterService()

        logger.debug("Starting TTML to TXT conversion")

        # Perform the conversion
        response = await converter_service.convert_ttml_to_txt(request)

        logger.debug(f"TTML conversion completed. Status: {response.status}")

        # Return appropriate HTTP status code based on conversion result
        if response.status == TaskStatus.COMPLETED:
            status_code = 200
        else:
            status_code = 400  # Bad request for conversion failures

        return JSONResponse(status_code=status_code, content=response.model_dump())

    except ValueError as e:
        # Invalid input data
        logger.warning(f"Validation error in TTML conversion: {e}")
        raise ValidationError(str(e))

    except Exception as e:
        # Unexpected errors
        logger.error(
            f"Unexpected error in TTML conversion endpoint: {str(e)}", exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content=ConvertTTMLResponse(
                status=TaskStatus.FAILED, error="Internal server error"
            ).model_dump(),
        )
