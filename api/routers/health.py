"""
Health check and monitoring endpoints.
"""

from datetime import UTC, datetime
from typing import Any

import psutil
from fastapi import APIRouter, Depends, Request, status
from fastapi.responses import JSONResponse
from loguru import logger

from api.middleware.auth import require_read
from api.middleware.error_handler import ServiceUnavailableError
from api.middleware.metrics import get_metrics
from core.config import get_settings
from models.database import db
from monitoring.alerts import alert_manager
from monitoring.log_aggregation import log_aggregator
from worker.task_manager import DEFAULT_QUEUE_SIZE as LIMIT_QUEUE_SIZE

router = APIRouter()
settings = get_settings()


def get_system_metrics() -> dict[str, Any]:
    """Get system resource metrics."""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory": {
                "percent": psutil.virtual_memory().percent,
                "available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
                "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            },
            "disk": {
                "percent": psutil.disk_usage("/").percent,
                "free_gb": round(psutil.disk_usage("/").free / (1024**3), 2),
                "total_gb": round(psutil.disk_usage("/").total / (1024**3), 2),
            },
            "load_average": (
                psutil.getloadavg() if hasattr(psutil, "getloadavg") else None
            ),
        }
    except Exception as e:
        logger.warning(f"Failed to get system metrics: {e}")
        return {"error": "Failed to collect system metrics"}


def check_service_health(task_queue) -> dict[str, Any]:
    """Check the health of various services."""
    health_status = {"overall": "healthy", "services": {}, "issues": []}

    try:
        # Check task queue
        queue_sizes = task_queue.get_queue_sizes()
        active_tasks = len(task_queue.active_async_tasks)
        max_workers = (
            task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
        )

        queue_health = "healthy"
        if any(size >= LIMIT_QUEUE_SIZE for size in queue_sizes.values()):
            queue_health = "overloaded"
            health_status["issues"].append("One or more queues are at capacity")
        elif active_tasks >= max_workers:
            queue_health = "overloaded"
            health_status["issues"].append("Maximum worker capacity reached")
        elif any(size >= LIMIT_QUEUE_SIZE * 0.8 for size in queue_sizes.values()):
            queue_health = "warning"
            health_status["issues"].append("Queues approaching capacity")

        health_status["services"]["task_queue"] = {
            "status": queue_health,
            "queue_sizes": queue_sizes,
            "active_tasks": active_tasks,
            "max_workers": max_workers,
        }

        # Check system resources
        system_metrics = get_system_metrics()
        if "error" not in system_metrics:
            system_health = "healthy"
            if system_metrics["cpu_percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("High CPU usage")
            if system_metrics["memory"]["percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("High memory usage")
            if system_metrics["disk"]["percent"] > 90:
                system_health = "warning"
                health_status["issues"].append("Low disk space")

            health_status["services"]["system"] = {
                "status": system_health,
                "metrics": system_metrics,
            }

        # Check database health
        db_health_result = db.health_check()
        db_health = "healthy" if db_health_result["status"] == "healthy" else "error"

        if db_health == "error":
            health_status["issues"].append(
                f"Database health check failed: {db_health_result.get('error', 'Unknown error')}"
            )

        health_status["services"]["database"] = {
            "status": db_health,
            "details": db_health_result,
        }

        # Determine overall health
        service_statuses = [
            service["status"] for service in health_status["services"].values()
        ]
        if "overloaded" in service_statuses:
            health_status["overall"] = "overloaded"
        elif "warning" in service_statuses or "error" in service_statuses:
            health_status["overall"] = (
                "warning" if "error" not in service_statuses else "error"
            )

    except Exception as e:
        logger.error(f"Error checking service health: {e}", exc_info=True)
        health_status["overall"] = "error"
        health_status["issues"].append(f"Health check failed: {str(e)}")

    return health_status


@router.post("/ping", status_code=status.HTTP_200_OK)
async def ping(request: Request):
    """
    Simple ping endpoint for basic availability check.

    Returns:
        JSONResponse: Simple pong response or 503 if overloaded
    """
    try:
        task_queue = request.app.state.task_queue
        queue_sizes = task_queue.get_queue_sizes()

        overload = any(size >= LIMIT_QUEUE_SIZE for size in queue_sizes.values())

        if overload:
            raise ServiceUnavailableError("Server queue overloaded")

        return JSONResponse(
            content={"message": "pong", "timestamp": datetime.now(UTC).isoformat()},
            status_code=200,
        )

    except ServiceUnavailableError:
        raise
    except Exception as e:
        logger.error(f"Error in /ping endpoint: {str(e)}", exc_info=True)
        return JSONResponse(content={"error": "Internal server error"}, status_code=500)


@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check(request: Request):
    """
    Comprehensive health check endpoint.

    Returns:
        JSONResponse: Detailed health information
    """
    try:
        task_queue = request.app.state.task_queue
        health_status = check_service_health(task_queue)

        # Add application info
        health_status["application"] = {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": getattr(settings, "ENVIRONMENT", "development"),
            "timestamp": datetime.now(UTC).isoformat(),
        }

        # Determine HTTP status code based on health
        if health_status["overall"] == "overloaded":
            status_code = 503
        elif health_status["overall"] == "error":
            status_code = 500
        elif health_status["overall"] == "warning":
            status_code = 200  # Still operational
        else:
            status_code = 200

        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Error in /health endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={
                "overall": "error",
                "error": "Health check failed",
                "timestamp": datetime.now(UTC).isoformat(),
            },
            status_code=500,
        )


@router.get("/metrics", status_code=status.HTTP_200_OK)
async def get_application_metrics(
    request: Request, user: dict[str, Any] = Depends(require_read)
):
    """
    Get application metrics.

    Requires: read permission

    Returns:
        JSONResponse: Application metrics
    """
    try:
        task_queue = request.app.state.task_queue

        # Get application metrics
        app_metrics = get_metrics()

        # Get queue metrics
        queue_metrics = {
            "queue_sizes": task_queue.get_queue_sizes(),
            "active_tasks": len(task_queue.active_async_tasks),
            "max_workers": {
                "subtitle": task_queue.LIMIT_SUBTITLE_WORKERS,
                "summarize": task_queue.LIMIT_SUMMARIZE_WORKERS,
                "total": task_queue.LIMIT_SUBTITLE_WORKERS
                + task_queue.LIMIT_SUMMARIZE_WORKERS,
            },
        }

        # Get system metrics
        system_metrics = get_system_metrics()

        # Get database metrics
        db_metrics = {
            "health": db.health_check(),
            "pool_status": db.get_pool_status(),
        }

        metrics = {
            "timestamp": datetime.now(UTC).isoformat(),
            "application": app_metrics,
            "queues": queue_metrics,
            "system": system_metrics,
            "database": db_metrics,
        }

        return JSONResponse(content=metrics, status_code=200)

    except Exception as e:
        logger.error(f"Error in /metrics endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={"error": "Failed to collect metrics"}, status_code=500
        )


@router.get("/alerts", status_code=status.HTTP_200_OK)
async def get_alerts(
    request: Request,
    user: dict[str, Any] = Depends(require_read),
    active_only: bool = False,
    limit: int = 50,
):
    """
    Get application alerts.

    Requires: read permission

    Args:
        active_only: If True, return only active alerts
        limit: Maximum number of alerts to return (for history)

    Returns:
        JSONResponse: Application alerts
    """
    try:
        if active_only:
            alerts = alert_manager.get_active_alerts()
        else:
            alerts = alert_manager.get_alert_history(limit=limit)

        # Convert alerts to dictionaries
        alerts_data = [alert.to_dict() for alert in alerts]

        response_data = {
            "timestamp": datetime.now(UTC).isoformat(),
            "active_count": len(alert_manager.get_active_alerts()),
            "total_count": len(alerts_data),
            "alerts": alerts_data,
        }

        return JSONResponse(content=response_data, status_code=200)

    except Exception as e:
        logger.error(f"Error in /alerts endpoint: {str(e)}", exc_info=True)
        return JSONResponse(content={"error": "Failed to get alerts"}, status_code=500)


@router.post("/alerts/{alert_name}/acknowledge", status_code=status.HTTP_200_OK)
async def acknowledge_alert(
    alert_name: str, user: dict[str, Any] = Depends(require_read)
):
    """
    Acknowledge an active alert.

    Requires: read permission

    Args:
        alert_name: Name of the alert to acknowledge

    Returns:
        JSONResponse: Acknowledgment result
    """
    try:
        success = alert_manager.acknowledge_alert(alert_name)

        if success:
            return JSONResponse(
                content={
                    "message": f"Alert '{alert_name}' acknowledged successfully",
                    "timestamp": datetime.now(UTC).isoformat(),
                },
                status_code=200,
            )
        else:
            return JSONResponse(
                content={
                    "error": f"Alert '{alert_name}' not found or not active",
                    "timestamp": datetime.now(UTC).isoformat(),
                },
                status_code=404,
            )

    except Exception as e:
        logger.error(f"Error acknowledging alert {alert_name}: {str(e)}", exc_info=True)
        return JSONResponse(
            content={"error": "Failed to acknowledge alert"}, status_code=500
        )


@router.get("/logs", status_code=status.HTTP_200_OK)
async def get_logs(
    user: dict[str, Any] = Depends(require_read),
    limit: int = 100,
    level: str | None = None,
    module: str | None = None,
    search: str | None = None,
):
    """
    Get application logs.

    Requires: read permission

    Args:
        limit: Maximum number of log entries to return
        level: Filter by log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        module: Filter by module name
        search: Search in log messages

    Returns:
        JSONResponse: Application logs
    """
    try:
        if search:
            logs = log_aggregator.search_logs(search, limit=limit)
        else:
            logs = log_aggregator.get_recent_logs(
                limit=limit, level_filter=level, module_filter=module
            )

        response_data = {
            "timestamp": datetime.now(UTC).isoformat(),
            "total_entries": len(logs),
            "filters": {
                "level": level,
                "module": module,
                "search": search,
                "limit": limit,
            },
            "logs": logs,
        }

        return JSONResponse(content=response_data, status_code=200)

    except Exception as e:
        logger.error(f"Error in /logs endpoint: {str(e)}", exc_info=True)
        return JSONResponse(content={"error": "Failed to get logs"}, status_code=500)


@router.get("/logs/stats", status_code=status.HTTP_200_OK)
async def get_log_statistics(user: dict[str, Any] = Depends(require_read)):
    """
    Get log statistics and metrics.

    Requires: read permission

    Returns:
        JSONResponse: Log statistics
    """
    try:
        stats = log_aggregator.get_log_statistics()
        error_analysis = log_aggregator.get_error_analysis()

        response_data = {
            "timestamp": datetime.now(UTC).isoformat(),
            "statistics": stats,
            "error_analysis": error_analysis,
        }

        return JSONResponse(content=response_data, status_code=200)

    except Exception as e:
        logger.error(f"Error in /logs/stats endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={"error": "Failed to get log statistics"}, status_code=500
        )
