"""
Text summarization endpoints.
"""

import asyncio
import builtins
import hashlib
from typing import Any

from fastapi import APIRouter, Depends, File, Request, UploadFile
from fastapi.responses import JSONResponse
from loguru import logger

from api.middleware.auth import require_write
from api.middleware.error_handler import (
    ServiceUnavailableError,
    TimeoutError,
    ValidationError,
)
from models.schemas import (
    FileType,
    SummarizeMode,
    SummarizeRequest,
    SummarizeResponse,
    TaskStatus,
)

router = APIRouter()


def _check_summarize_overload(task_queue):
    """Check if summarize service is overloaded and raise appropriate error."""
    active_tasks_count = len(task_queue.active_async_tasks)
    max_total_workers = (
        task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
    )
    is_summarize_queue_full = task_queue.summarize_queue.full()

    if is_summarize_queue_full:
        raise ServiceUnavailableError(
            "Summarization task queue is full. Please try again later."
        )

    if active_tasks_count >= max_total_workers:
        raise ServiceUnavailableError(
            "Server is at maximum processing capacity. Please try again later."
        )


@router.post("/summarize", response_model=SummarizeResponse)
async def create_summarize_task(
    request: SummarizeRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write),
):
    """
    Submit a text for summarization.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(
        f"Received /summarize request, text length: {len(request.og_text)}, mode: {request.mode}"
    )

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_summarize_overload(task_queue)

    try:
        logger.debug(
            f"Attempting to add task for text (length: {len(request.og_text)}) to summarize task queue."
        )
        response = await asyncio.wait_for(
            task_queue.add_summarize_task(
                request.og_text,
                mode=request.mode.value if request.mode else "default",
                client_uid=None,  # Explicitly set to None - deprecated field
            ),
            timeout=5.0,
        )
        logger.debug(
            f"Summarize task added. Response from add_summarize_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except builtins.TimeoutError:
        logger.error(
            f"Timeout adding summarization task to queue for text length: {len(request.og_text)}",
            exc_info=True,
        )
        raise TimeoutError("Server is busy, please try again later")


@router.post("/summarize/file", response_model=SummarizeResponse)
async def create_summarize_file_task(
    file: UploadFile = File(...),
    mode: SummarizeMode = None,
    client_uid: str | None = None,
    req: Request = None,
    user: dict[str, Any] = Depends(require_write),
):
    """
    Submit a file for text summarization.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if client_uid:
        logger.debug(
            f"Received deprecated client_uid: {client_uid} (ignored for processing)"
        )

    logger.debug(
        f"Received /summarize/file request, filename: {file.filename}, mode: {mode}"
    )

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_summarize_overload(task_queue)

    # Validate file type
    if file.content_type not in [FileType.TXT.value, FileType.MD.value]:
        raise ValidationError(
            f"Invalid file type. Supported types: {[t.value for t in FileType]}"
        )

    try:
        # Read file content
        content = await file.read()
        text = content.decode("utf-8")

        # Validate text content
        if not text.strip():
            raise ValidationError("File is empty or contains no valid text")

        logger.info(
            f"Received file '{file.filename}' for summarization, size: {len(content)} bytes"
        )

        # Generate a hash of the text to use as task_id
        text_hash = hashlib.sha256(text.encode("utf-8")).hexdigest()
        logger.debug(f"Generated text_hash for file {file.filename}: {text_hash}")

        # Create summarization task
        logger.debug(
            f"Attempting to add task for file {file.filename} (hash: {text_hash}) to summarize task queue."
        )
        response = await task_queue.add_summarize_task(
            text=text,
            mode=mode.value if mode else "default",
            client_uid=None,  # Explicitly set to None - deprecated field
        )
        logger.debug(
            f"Summarize file task added for {file.filename}. Response from add_summarize_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except UnicodeDecodeError:
        logger.error(
            f"UnicodeDecodeError for file {file.filename}.",
            exc_info=True,
        )
        raise ValidationError("File must be a valid UTF-8 text file")
