"""
Task management endpoints.
"""

from typing import Any

from fastapi import APIRouter, Depends, Request
from loguru import logger

from api.middleware.auth import require_read
from api.middleware.error_handler import NotFoundError
from models.schemas import SubtitleResponse, SummarizeResponse

router = APIRouter()


@router.get("/task/{task_id}", response_model=SubtitleResponse | SummarizeResponse)
async def get_task(
    task_id: str, request: Request, user: dict[str, Any] = Depends(require_read)
):
    """
    Get the status or result of any task (subtitles or summarization).

    Requires: read permission
    """
    task_queue = request.app.state.task_queue

    logger.debug(f"Received request to get task status for task_id: {task_id}")

    try:
        # get_task is a synchronous method, not async
        task = task_queue.get_task(task_id)
        if task is None:
            logger.debug(f"Task not found: {task_id}")
            raise NotFoundError(f"Task with ID '{task_id}' not found")

        logger.debug(f"Task found: {task_id}, status: {task.status}")
        return task

    except Exception as e:
        logger.error(f"Error retrieving task {task_id}: {e}", exc_info=True)
        raise NotFoundError(f"Task with ID '{task_id}' not found")
